import ClubUI from "Components/Shared/ClubUI";
import { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import Select from "react-select";
import LoadingSpinner from "Components/LoadingSpinner";
import { FaUserShield } from "react-icons/fa";
import NoClubSelected from "Components/Shared/NoClubSelected";

let sdk = new MkdSDK();
export default function AdminListClubUI() {
  const [profileSettings, setProfileSettings] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [selectedClub, setSelectedClub] = useState(null);
  const [surfaces, setSurfaces] = useState([]);
  const [sports, setSports] = useState([]);
  const [roleAccess, setRoleAccess] = useState(null);
  async function fetchClubs() {
    setIsLoading(true);
    try {
      sdk.setTable("clubs");
      const response = await sdk.callRestAPI({}, "GETALL");
      setClubs(response.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  }
  const fetchRoleAccess = async (clubId) => {
    sdk.setTable("club_permissions");
    const result = await sdk.callRestAPI(
      {
        filter: [`club_id,eq,${clubId}`],
      },
      "GETALL"
    );
    if (result.list.length > 0) {
      return JSON.parse(result.list[0].permission);
    }
    return null;
  };
  const handleSelectClub = async (club) => {
    setIsLoading(true);
    try {
      setSelectedClub({ id: club.value, name: club.label });
      await fetchSettings(club.value, club.club_id);
      // setCourts(courtsResponse.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSettings = async (clubUserId, club_id) => {
    setIsLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${clubUserId}`,
        {},
        "GET"
      );
      const roleAccess = await fetchRoleAccess(club_id);
      setRoleAccess(roleAccess);
      const fetchSurfaces = async () => {
        sdk.setTable("surface");
        const surfaces = await sdk.callRestAPI({}, "GETALL");
        setSurfaces(surfaces?.list);
      };

      const fetchSports = async () => {
        sdk.setTable("sports");
        const sports = await sdk.callRestAPI(
          { filter: [`club_id,cs,${response?.model?.club?.id}`] },
          "GETALL"
        );
        setSports(sports?.list);
      };
      fetchSurfaces();
      fetchSports();
      setProfileSettings(response?.model);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchClubs();
  }, []);
  console.log("roleAccess", roleAccess);
  console.log("profileSettings", profileSettings);
  return (
    <div className="h-full bg-white p-4 sm:p-6 lg:p-8">
      {isLoading && <LoadingSpinner />}
      <div className="mb-4 max-w-xl">
        <label className="mb-2 block text-base font-medium text-gray-900">
          Select club
        </label>
        <Select
          className="w-full rounded-lg border border-gray-200 p-2.5 text-sm"
          options={clubs.map((club) => ({
            value: club.user_id,
            label: club.name,
            club_id: club?.id,
          }))}
          isMulti={false}
          onChange={handleSelectClub}
        />
      </div>
      {selectedClub?.id ? (
        roleAccess && !roleAccess.admin.club_ui ? (
          <div className="flex h-[80vh] flex-col items-center justify-center">
            <FaUserShield className="mb-4 text-6xl text-gray-400" />
            <h2 className="mb-2 text-xl font-semibold text-gray-900">
              Access Restricted
            </h2>
            <p className="text-center text-gray-600">
              You don't have permission to access the club UI for this club
            </p>
          </div>
        ) : (
          <ClubUI
            selectedClub={selectedClub}
            profileSettings={profileSettings}
            club={profileSettings?.club}
            fetchSettings={fetchSettings}
            courts={profileSettings?.courts}
            sports={profileSettings?.sports}
            pricing={profileSettings?.pricing}
            clubUser={profileSettings?.user}
          />
        )
      ) : (
        <NoClubSelected />
      )}
    </div>
  );
}
