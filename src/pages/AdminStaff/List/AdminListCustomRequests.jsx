import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import Skeleton from "react-loading-skeleton";
import { ModalSidebar } from "Components/ModalSidebar";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { FaArrowRight } from "react-icons/fa";
import { HiOutlineTrash } from "react-icons/hi";
// import AddAdminEmailPage from "../Add/AddAdminEmailPage";
// import EditAdminEmailPage from "../Edit/EditAdminEmailPage";
import { AddButton } from "Components/AddButton";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";

let sdk = new MkdSDK();

const columns = [
  {
    header: "Date",
    accessor: "create_at",
  },
  {
    header: "Requested by",
    accessor: "num_players_needed",
  },
  {
    header: "Number of Players",
    accessor: "players",
  },
  {
    header: "Coach preferred",
    accessor: "user_id",
  },
  {
    header: "Email reply",
    accessor: "start_time",
  },
  {
    header: "Status",
    accessor: "status",
  },

  {
    header: "",
    accessor: "actions",
  },
];

const AdminListCustomRequests = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [showFilterOptions, setShowFilterOptions] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [optionValue, setOptionValue] = React.useState("eq");
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [activeEditId, setActiveEditId] = React.useState();
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectRole = ["", "admin", "employee"];
  const selectStatus = [
    { key: "", value: "All" },
    { key: "0", value: "Inactive" },
    { key: "1", value: "Active" },
    { key: "2", value: "Suspend" },
  ];

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  const addFilterCondition = (option, selectedValue, inputValue) => {
    const input =
      selectedValue === "eq" && isNaN(inputValue)
        ? `${inputValue}`
        : inputValue;
    const condition = `${option},${selectedValue},${input}`;
    setFilterConditions((prevConditions) => {
      const newConditions = prevConditions.filter(
        (condition) => !condition.includes(option)
      );
      return [...newConditions, condition];
    });
  };

  const handleFilter = () => {
    getData(0, pageSize, {}, filterConditions);
  };
  const deleteFilter = (deleted) => {
    getData(0, pageSize, {}, deleted);
  };

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      sdk.setTable("custom_requests");

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: filters,
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onSubmit = (data) => {
    if (data.search) {
      getData(0, pageSize, {}, [
        `first_name,cs,${data.search}`,
        `last_name,cs,${data.search}`,
      ]);
    } else {
      getData(0, pageSize);
    }
  };
  const onStatusSelect = (e) => {
    if (e.target.value === "") {
      getData(0, pageSize);
    } else {
      getData(0, pageSize, {}, [`status,cs,${e.target.value}`]);
    }
  };
  const onAgeGroupSelect = (e) => {
    getData(0, pageSize, {}, [`age_group,cs,${e.target.value}`]);
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "custom-requests",
      },
    });

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const clearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    getData(1, pageSize);
  };

  const handleDelete = async (id) => {
    if (window.confirm("Are you sure you want to delete this clinic?")) {
      try {
        sdk.setTable("find_a_buddy");
        await sdk.callRestAPI({ id }, "DELETE");
        getData(currentPage, pageSize); // Refresh the data
      } catch (error) {
        console.error("Error deleting clinic:", error);
        tokenExpireError(dispatch, error.message);
      }
    }
  };

  return (
    <div className="h-screen px-8">
      <div className="flex flex-col items-start justify-between gap-4 py-3 md:flex-row lg:items-center">
        <form
          className="relative flex w-full min-w-[300px] max-w-[300px] flex-1 items-center"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <BiSearch className="text-gray-500" />
          </div>
          <input
            type="text"
            className="block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="search..."
            {...register("search")}
          />
          <button
            type="submit"
            className="absolute bottom-0 right-0 top-0 px-3 text-gray-500 hover:text-gray-700"
          >
            <BiSearch className="h-5 w-5" />
          </button>
        </form>

        <div className="flex flex-wrap items-center gap-4">
          <select
            className="rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
            defaultValue="All"
            onChange={onStatusSelect}
          >
            <option value="">Status: All</option>
            <option value="0">Inactive</option>
            <option value="1">Active</option>
            {/* <option value="2">Suspend</option> */}
          </select>
          <select
            className="rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
            defaultValue="All"
            // onChange={onAgeGroupSelect}
          >
            <option>Viewing: Royal Club</option>
            <option>Viewing: Royal Club</option>
            <option>Viewing: Royal Club</option>
          </select>
          <button
            onClick={() => navigate("/club/program-clinics/add")}
            className="inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10 6.45703V9.9987L12.9167 12.9154"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M2.29102 3.95703V7.29036H5.62435"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M2.70898 12.5707C3.76873 15.5646 6.62818 17.7096 9.98935 17.7096C14.2528 17.7096 17.709 14.2585 17.709 10.0013C17.709 5.74411 14.2528 2.29297 9.98935 2.29297C6.79072 2.29297 4.04646 4.23552 2.87521 7.00362"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            History
          </button>
          <button
            onClick={() => navigate("/club/program-clinics/add")}
            className="inline-flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-4 py-2 text-sm font-semibold text-gray-500 hover:bg-gray-100"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.70898 6.25L5.60038 3.35861C5.84445 3.11453 6.24018 3.11453 6.48426 3.35861L9.37565 6.25M10.6257 13.75L13.517 16.6414C13.7611 16.8855 14.1568 16.8855 14.4009 16.6414L17.2923 13.75M6.04232 4.16667L6.04232 16.875M13.959 3.125L13.959 16.0417"
                stroke="#868C98"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            Sort by: time
          </button>

          {/* <button className="inline-flex items-center gap-2 rounded-md border border-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path
                d="M3.33301 10H16.6663M3.33301 5H16.6663M3.33301 15H16.6663"
                stroke="currentColor"
                strokeWidth="1.67"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            History
          </button> */}
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto rounded-lg border border-gray-200 bg-white">
          <table className="w-full min-w-[1000px] table-auto divide-y divide-gray-200">
            <thead>
              <tr>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    scope="col"
                    className="whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500"
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y-8 divide-gray-50">
              {data.map((row, i) => {
                return (
                  <tr key={i} className="hover:bg-gray-50">
                    {columns.map((cell, index) => {
                      if (cell.accessor == "") {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            <div className="flex items-center  gap-3">
                              <button className="rounded-full p-2 hover:bg-gray-100">
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z"
                                    stroke="#868C98"
                                    stroke-width="1.5"
                                    stroke-linecap="square"
                                    stroke-linejoin="round"
                                  />
                                </svg>
                              </button>
                              <button className="rounded-full p-2 hover:bg-gray-100">
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M11.041 5.20951L13.5768 2.67377C13.9022 2.34833 14.4298 2.34833 14.7553 2.67376L17.3268 5.24525C17.6522 5.57069 17.6522 6.09833 17.3268 6.42377L14.791 8.95951M11.041 5.20951L2.53509 13.7154C2.37881 13.8717 2.29102 14.0837 2.29102 14.3047V17.7095H5.69584C5.91685 17.7095 6.12881 17.6217 6.28509 17.4654L14.791 8.95951M11.041 5.20951L14.791 8.95951"
                                    stroke="#868C98"
                                    stroke-width="1.5"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                  />
                                </svg>
                              </button>
                              <button
                                onClick={() => handleDelete(row.id)}
                                className="rounded-full p-2 hover:bg-gray-100"
                              >
                                <svg
                                  width="20"
                                  height="20"
                                  viewBox="0 0 20 20"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z"
                                    fill="#868C98"
                                  />
                                </svg>
                              </button>
                            </div>
                          </td>
                        );
                      }
                      if (cell.mapping && cell.accessor === "status") {
                        return (
                          <td
                            key={index}
                            className="inline-block whitespace-nowrap px-6 py-5 text-sm"
                          >
                            {row[cell.accessor] === 1 ? (
                              <span className="rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]">
                                {cell.mapping[row[cell.accessor]]}
                              </span>
                            ) : (
                              <span className="rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]">
                                {cell.mapping[row[cell.accessor]]}
                              </span>
                            )}
                          </td>
                        );
                      }
                      if (cell.mapping) {
                        return (
                          <td
                            key={index}
                            className="whitespace-nowrap px-6 py-4"
                          >
                            {cell.mapping[row[cell.accessor]]}
                          </td>
                        );
                      }
                      return (
                        <td key={index} className="whitespace-nowrap px-6 py-4">
                          {row[cell.accessor]}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
          {loading && (
            <div className="px-6 py-4">
              <p className="text-gray-500">Loading...</p>
            </div>
          )}
          {!loading && data.length === 0 && (
            <div className="w-full px-6 py-4 text-center">
              <p className="text-gray-500">No data available</p>
            </div>
          )}
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
      {/* <ModalSidebar
        isModalActive={showAddSidebar}
        closeModalFn={() => setShowAddSidebar(false)}
      >
        <AddAdminEmailPage setSidebar={setShowAddSidebar} getData={getData} />
      </ModalSidebar> */}
      {/* {showEditSidebar && (
        <ModalSidebar
          isModalActive={showEditSidebar}
          closeModalFn={() => setShowEditSidebar(false)}
        >
          <EditAdminEmailPage
            activeId={activeEditId}
            setSidebar={setShowEditSidebar}
          />
        </ModalSidebar>
      )} */}
    </div>
  );
};

export default AdminListCustomRequests;
