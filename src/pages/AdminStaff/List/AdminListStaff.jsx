import { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import Select from "react-select";
import LoadingSpinner from "Components/LoadingSpinner";
import { fetchRoleAccess } from "Utils/roleAccess";
import AccessRestricted from "Components/Shared/AccessRestricted";
import ListStaff from "Components/Shared/ListStaff";
import NoClubSelected from "Components/Shared/NoClubSelected";

let sdk = new MkdSDK();
export default function AdminListReservation() {
  const [profileSettings, setProfileSettings] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [selectedClub, setSelectedClub] = useState(null);
  const [sports, setSports] = useState([]);
  const [roleAccess, setRoleAccess] = useState(null);
  async function fetchClubs() {
    setIsLoading(true);
    try {
      sdk.setTable("clubs");
      const response = await sdk.callRestAPI({}, "GETALL");
      setClubs(response.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  }
  const handleSelectClub = async (club) => {
    setIsLoading(true);
    try {
      setSelectedClub({
        id: club.value,
        name: club.label,
        club_id: club.club_id,
      });
      await fetchSettings(club.value);
      const roleAccess = await fetchRoleAccess(club.club_id);
      setRoleAccess(roleAccess);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSettings = async (clubId) => {
    setIsLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${clubId}`,
        {},
        "GET"
      );

      setProfileSettings(response?.model?.club);
      setSports(response?.model?.sports);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchClubs();
  }, []);
  return (
    <div className="h-full bg-white p-4 sm:p-6 lg:p-8">
      {isLoading && <LoadingSpinner />}
      <div className="mb-4 max-w-xl">
        <label className="mb-2 block text-base font-medium text-gray-900">
          Select club
        </label>
        <Select
          className="w-full rounded-lg border border-gray-200 p-2.5 text-sm"
          options={clubs.map((club) => ({
            value: club.user_id,
            label: club.name,
            club_id: club?.id,
          }))}
          isMulti={false}
          onChange={handleSelectClub}
        />
      </div>
      {profileSettings?.id ? (
        <>
          {roleAccess && roleAccess.admin.staff ? (
            <ListStaff club={profileSettings} sports={sports} />
          ) : (
            <AccessRestricted message="You don't have permission to access the reservations" />
          )}
        </>
      ) : (
        <NoClubSelected />
      )}
    </div>
  );
}
