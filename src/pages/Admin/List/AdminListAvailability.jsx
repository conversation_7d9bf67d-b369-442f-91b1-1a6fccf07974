import Availability from "Components/Shared/Availability";
import { useState, useEffect } from "react";
import MkdSDK from "Utils/MkdSDK";
import Select from "react-select";
import LoadingSpinner from "Components/LoadingSpinner";
import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";

let sdk = new MkdSDK();
function AdminListAvailability() {
  const [profileSettings, setProfileSettings] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [selectedClub, setSelectedClub] = useState(null);
  const [coaches, setCoaches] = useState([]);
  const [staff, setStaff] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [courts, setCourts] = useState([]);

  async function fetchClubs() {
    setIsLoading(true);
    try {
      sdk.setTable("clubs");
      const response = await sdk.callRestAPI({}, "GETALL");
      setClubs(response.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  }
  const fetchSettings = async (clubUserId) => {
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${clubUserId}`,
        {},
        "GET"
      );
      setProfileSettings(response?.model);
      console.log("response", response);
      // setCourts(response?.model?.courts);
    } catch (error) {
      console.log(error);
    }
  };

  const fetchCourts = async (clubId) => {
    sdk.setTable("club_court");
    const courts = await sdk.callRestAPI(
      { filter: [`club_id,cs,${clubId}`] },
      "GETALL"
    );
    setCourts(courts?.list);
  };

  const fetchBookings = async (clubId) => {
    sdk.setTable("booking");
    const bookings = await sdk.callRestAPI(
      {
        // filter: [`club_id,cs,${clubId}`],
      },
      "GETALL"
    );
    setBookings(bookings?.list);
  };

  const fetchCoaches = async (clubId) => {
    sdk.setTable("user");
    const coaches = await sdk.callRestAPI(
      {
        filter: [`club_id,cs,${clubId}`, "role,cs,coach"],
      },
      "GETALL"
    );
    setCoaches(coaches?.list);
  };

  const fetchStaff = async (clubId) => {
    sdk.setTable("user");
    const staff = await sdk.callRestAPI(
      {
        filter: [`club_id,cs,${clubId}`, "role,cs,staff"],
      },
      "GETALL"
    );
    setStaff(staff?.list);
  };

  const handleSelectClub = async (club) => {
    setIsLoading(true);
    try {
      await fetchCourts(club.value);
      await fetchBookings(club.value);
      await fetchCoaches(club.value);
      await fetchStaff(club.value);
      setSelectedClub(club.value);
      // setCourts(courtsResponse.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  console.log({
    courts,
    bookings,
    coaches,
    staff,
  });
  useEffect(() => {
    fetchClubs();
  }, []);
  return (
    <div className="h-full bg-white p-4 sm:p-6 lg:p-8">
      {isLoading && <LoadingSpinner />}
      <div className="mb-4 max-w-xl">
        <label className="mb-2 block text-base font-medium text-gray-900">
          Select club
        </label>
        <Select
          className="w-full rounded-lg border border-gray-200 p-2.5 text-sm"
          options={clubs.map((club) => ({
            value: club?.id,
            label: club.name,
          }))}
          isMulti={false}
          onChange={handleSelectClub}
        />
      </div>

      <Availability
        selectedClub={selectedClub}
        coaches={coaches}
        staff={staff}
        courts={courts}
        bookings={bookings}
      />
    </div>
  );
}

// Export with permission wrapper
export default withAdminPermission(
  AdminListAvailability,
  "availability_dashboard",
  "You don't have permission to access availability dashboard"
);
