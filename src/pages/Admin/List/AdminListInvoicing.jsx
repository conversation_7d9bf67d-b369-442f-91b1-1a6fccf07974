import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import _ from "lodash";
import ClubSelector from "Components/ClubSelector";
import InvoiceList from "Components/Shared/InvoiceList";
import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";
import NoClubSelected from "Components/Shared/NoClubSelected";

let sdk = new MkdSDK();

const AdminListInvoicing = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [clubProfile, setClubProfile] = useState(null);
  const [clubId, setClubId] = useState(null);
  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      sdk.setTable("stripe_invoice");

      const result = await sdk.callRestAPI(
        {
          payload: {
            ...data,
          },
          page: pageNum,
          limit: limitNum,
          filter: filters,
        },
        "PAGINATE"
      );

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  async function fetchInvoices(page = 1, pageSize = 10, filters = {}) {
    setLoading(true);
    try {
      const {
        invoice_id = "",
        firstName = "",
        lastName = "",
        sportId = "",
        receiptId = "",
        bookingType = "",
        from = "",
        until = "",
        month = "",
        year = "",
        sort = "desc",
      } = filters;

      console.log("filters", filters);

      const queryParams = new URLSearchParams();

      if (firstName) queryParams.append("first_name", firstName);
      if (lastName) queryParams.append("last_name", lastName);
      if (sportId) queryParams.append("sport_id", sportId);
      if (receiptId) queryParams.append("receipt_id", receiptId);
      if (bookingType) queryParams.append("booking_type", bookingType);
      if (from) queryParams.append("from", from);
      if (until) queryParams.append("until", until);
      if (invoice_id) queryParams.append("receipt_id", invoice_id);
      if (month) queryParams.append("month", month);
      if (year) queryParams.append("year", year);
      if (sort) queryParams.append("sort", sort);
      // queryParams.append("page", page);
      queryParams.append("club_id", clubId);

      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/billing/invoices?${queryParams.toString()}`,
        {},
        "GET"
      );
      setInvoices(response.invoices);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  const fetchProfile = async () => {
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/club/profile/${clubId}`,
        {},
        "GET"
      );
      setClubProfile(response.model);
    } catch (error) {
      console.log("ERROR", error);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "invoicing",
      },
    });
    // fetchProfile();
    // const delay = 700;
    // const timeoutId = setTimeout(async () => {
    //   await getData(1, pageSize);
    // }, delay);

    // return () => {
    //   clearTimeout(timeoutId);
    // };
  }, []);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleClubSelect = async (club) => {
    setClubProfile(club);
    await setClubId(club.club?.id);
    console.log("club", club);
    await fetchInvoices();
  };

  console.log({ clubId });
  return (
    <div className="h-screen p-8">
      <ClubSelector onSelect={handleClubSelect} />
      {clubProfile?.id ? (
        <InvoiceList
          clubProfile={clubProfile}
          fetchProfile={fetchProfile}
          sports={clubProfile?.sports}
        />
      ) : (
        <NoClubSelected />
      )}
    </div>
  );
};

// Export with permission wrapper
export default withAdminPermission(
  AdminListInvoicing,
  "invoicing",
  "You don't have permission to access invoicing management"
);
