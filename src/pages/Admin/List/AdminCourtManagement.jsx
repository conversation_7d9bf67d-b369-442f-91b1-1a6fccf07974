import CourtManagement from "Components/CourtManagement";
import LoadingSpinner from "Components/LoadingSpinner";
import { useEffect, useState } from "react";
import Select from "react-select";
import MkdSDK from "Utils/MkdSDK";
import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";

let sdk = new MkdSDK();
function AdminCourtManagement() {
  const [profileSettings, setProfileSettings] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [sports, setSports] = useState([]);
  const [surfaces, setSurfaces] = useState([]);
  const [exceptions, setExceptions] = useState([]);

  const fetchSettings = async () => {
    setIsLoading(true);
    try {
      const response = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${profileSettings?.user?.id}`,
        {},
        "GET"
      );
      setProfileSettings(response?.model);

      // Safely parse exceptions and ensure it's an array
      try {
        const parsedExceptions = response.model?.club?.exceptions
          ? JSON.parse(response?.model?.club.exceptions)
          : [];
        setExceptions(Array.isArray(parsedExceptions) ? parsedExceptions : []);
      } catch (error) {
        console.error("Error parsing exceptions:", error);
        setExceptions([]);
      }
      console.log("response", response);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  };

  async function fetchClubs() {
    setIsLoading(true);
    try {
      sdk.setTable("clubs");
      const response = await sdk.callRestAPI({}, "GETALL");
      setClubs(response.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleSelectClub = async (club) => {
    setIsLoading(true);
    try {
      const clubProfileResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${club.user_id}`,
        {},
        "GET"
      );

      // setCourts(courtsResponse.list);
      sdk.setTable("sports");
      const sportsResponse = await sdk.callRestAPI(
        { filter: [`club_id,cs,${club.value}`] },
        "GETALL"
      );
      setSports(sportsResponse?.list);

      sdk.setTable("surface");
      const surfaces = await sdk.callRestAPI({}, "GETALL");
      setSurfaces(surfaces?.list);

      setProfileSettings(clubProfileResponse?.model);

      // Safely parse exceptions and ensure it's an array
      try {
        const parsedExceptions = clubProfileResponse?.model?.club?.exceptions
          ? JSON.parse(clubProfileResponse?.model?.club.exceptions)
          : [];
        setExceptions(Array.isArray(parsedExceptions) ? parsedExceptions : []);
      } catch (error) {
        console.error("Error parsing exceptions:", error);
        setExceptions([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchClubs();
  }, []);
  return (
    <div>
      {isLoading && <LoadingSpinner />}
      <div className="h-full bg-white p-4 sm:p-6 lg:p-8">
        <div className="mb-4 max-w-xl">
          <label className="mb-2 block text-base font-medium text-gray-900">
            Select club
          </label>
          <Select
            className="w-full rounded-lg border border-gray-200 p-2.5 text-sm"
            options={clubs.map((club) => ({
              value: club?.id,
              label: club.name,
              user_id: club.user_id,
            }))}
            isMulti={false}
            onChange={handleSelectClub}
          />
        </div>
        <CourtManagement
          profileSettings={profileSettings}
          setProfileSettings={setProfileSettings}
          sports={sports}
          exceptions={exceptions}
          fetchSettings={fetchSettings}
          setExceptions={setExceptions}
          club={profileSettings?.club}
          courts={
            profileSettings?.courts.length > 0
              ? profileSettings?.courts.map((court) => {
                  return {
                    ...court,
                    court_settings: court.court_settings
                      ? (() => {
                          try {
                            return JSON.parse(court.court_settings);
                          } catch (error) {
                            console.error(
                              "Error parsing court_setting:",
                              error
                            );
                            return {};
                          }
                        })()
                      : {},
                  };
                })
              : []
          }
          edit_api={`/v3/api/custom/courtmatchup/admin/profile-edit/${profileSettings?.user?.id}`}
        />
      </div>
    </div>
  );
}

// Export with permission wrapper
export default withAdminPermission(
  AdminCourtManagement,
  "court_management",
  "You don't have permission to access court management"
);
