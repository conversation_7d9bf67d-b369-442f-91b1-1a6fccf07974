import React, { useState } from "react";
import DailyScheduler from "Components/DailyScheduler";
import ReservationDetailsDrawer from "Components/Drawers/ReservationDetailsDrawer";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext } from "Context/Global";
import { BiSearch } from "react-icons/bi";
import EventForm from "Components/EventForm";
import Select from "react-select";
import LoadingSpinner from "Components/LoadingSpinner";
import TreeSDK from "Utils/TreeSDK";
import { eventTypeOptions } from "Utils/utils";
import withAdminPermission from "Components/PermissionWrapper/AdminPermissionWrapper";

const sdk = new MkdSDK();
const tdk = new TreeSDK();

function AdminListDailyScheduler() {
  const [events, setEvents] = useState([]);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [eventLoading, setEventLoading] = React.useState(false);
  const [showEventModal, setShowEventModal] = useState(false);
  const [clubs, setClubs] = useState([]);
  const [club, setClub] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [selectedSport, setSelectedSport] = useState(null);
  async function fetchData({ filters }) {
    setEventLoading(true);
    try {
      // sdk.setTable("booking");

      // const response = await sdk.callRestAPI(
      //   {
      //     filter: filters,
      //   },
      //   "GETALL"
      // );
      console.log("filters", filters);
      const response = await tdk.getList("reservation", {
        filter: [
          // `courtmatchup_reservation.club_id,cs,${globalState?.clubProfile?.club?.id}`,
          ...filters,
        ],
        join: [
          "booking|booking_id",
          "user|user_id",
          "buddy|buddy_id",
          "clubs|club_id",
        ],
      });

      console.log("reservations response", response);

      const formattedEvents = response.list.map((reservation) => ({
        id: reservation?.booking?.id,
        court_ids: reservation?.booking?.court_ids
          ? JSON.parse(reservation?.booking?.court_ids)
          : reservation?.booking?.court_id,
        startTime: reservation?.booking?.start_time,
        endTime: reservation?.booking?.end_time,
        date: reservation?.booking?.date,
        reservation_type: reservation?.booking?.reservation_type,
        title: eventTypeOptions.find(
          (option) => option.value == reservation?.booking?.reservation_type
        )?.label,
        status: reservation?.booking?.status,
        price: reservation?.booking?.price,
        duration: reservation?.booking?.duration,
        players: reservation?.booking?.player_ids
          ? JSON.parse(reservation?.booking?.player_ids)
          : [],
        sport: club?.sports.find(
          (sport) => sport.id == reservation?.booking?.sport_id
        ),
        user_id: reservation?.booking?.user_id,
        booking: reservation?.booking,
        user: reservation?.user,
        buddy: reservation?.buddy,
        club: reservation?.clubs,
        type: reservation?.booking?.type,
        sub_type: reservation?.booking?.subtype,
        court_fee: reservation?.booking?.court_fee,
        sport_id: reservation?.booking?.sport_id,
      }));

      setEvents(formattedEvents);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setEventLoading(false);
    }
  }

  async function fetchClubs() {
    setIsLoading(true);
    try {
      sdk.setTable("clubs");
      const response = await sdk.callRestAPI({}, "GETALL");
      setClubs(response.list);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  }

  const fetchUsers = async () => {
    try {
      sdk.setTable("user");
      const response = await sdk.callRestAPI(
        {
          filter: [],
        },
        "GETALL"
      );
      setUsers(response.list);
      console.log("users", response);
    } catch (error) {
      console.log(error);
    }
  };

  const handleFilterBySport = (sportId) => {
    const sport = club?.sports.find((s) => s.id === sportId);
    setSelectedSport(sport);
    const filter = `courtmatchup_booking.sport_id,cs,${sportId}`;
    fetchData({ filters: [filter] });
  };

  const handleAddEvent = () => {
    if (club?.club?.id) {
      setShowEventModal(true);
    } else {
      toast.error("Please select a club first");
    }
  };

  const handleSelectClub = async (club) => {
    setIsLoading(true);
    try {
      const clubProfileResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/admin/profile/${club.value}`,
        {},
        "GET"
      );

      fetchData({
        filters: [`courtmatchup_reservation.club_id,eq,${club?.id}`],
      });
      fetchUsers();

      setClub(clubProfileResponse?.model);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "daily-scheduler",
      },
    });
    fetchClubs();
  }, []);

  return (
    <div className="h-full bg-white p-4">
      <div className="mb-4 max-w-xl">
        <label className="mb-2 block text-base font-medium text-gray-900">
          Select club
        </label>
        <Select
          className="z-50 w-full rounded-lg border border-gray-200 p-2.5 text-sm"
          options={clubs.map((club) => ({
            value: club.user_id,
            label: club.name,
            id: club?.id,
          }))}
          isMulti={false}
          onChange={handleSelectClub}
          styles={{ zIndex: 9999 }}
        />
      </div>
      {club && (
        <>
          <div className="flex w-full flex-col">
            <div className="mb-6 flex flex-col space-y-4 ">
              {/* Search Section */}
              <div className="flex w-full items-center justify-between">
                <div className="relative flex items-center">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <BiSearch className="h-5 w-5 text-gray-500" />
                  </div>
                  <input
                    type="text"
                    className="block w-full rounded-md border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Search players..."
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2 sm:flex-nowrap sm:space-x-2">
                  {/* History Button */}
                  <button className="flex items-center space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                    <svg
                      className="h-5 w-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span className="hidden sm:inline">History</span>
                    <span className="sm:hidden">Hist</span>
                  </button>

                  {/* Add Event Button */}
                  {club?.club?.id && (
                    <button
                      onClick={handleAddEvent}
                      className="hover:bg-navy-800 flex items-center space-x-2 rounded-lg bg-navy-700 px-3 py-2 text-sm font-medium text-white"
                    >
                      <span>+</span>
                      <span>Add event</span>
                    </button>
                  )}
                </div>
              </div>

              {/* Controls Section */}
              <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:space-x-4 lg:space-y-0">
                {/* Sports Filter */}
                <div className="flex max-w-fit flex-wrap gap-2 rounded-lg border bg-white p-2 sm:flex-nowrap sm:gap-0 sm:overflow-hidden sm:p-0">
                  {club?.sports.length > 0 &&
                    club?.sports.map((sport, index) => (
                      <button
                        key={index}
                        onClick={() => {
                          handleFilterBySport(sport.id);
                        }}
                        className={`whitespace-nowrap rounded px-3 py-2 text-sm font-medium transition-colors sm:rounded-none ${
                          selectedSport?.id === sport.id
                            ? "bg-navy-700 text-white"
                            : "text-gray-600 hover:bg-gray-100"
                        }`}
                      >
                        {sport.name}
                      </button>
                    ))}
                </div>
              </div>
            </div>
          </div>

          <DailyScheduler
            sports={club?.sports}
            events={events}
            loading={eventLoading}
            onDateChange={fetchData}
            // clubId={club?.id}
            fetchData={fetchData}
            onEventClick={setSelectedEvent}
            courts={
              club?.courts?.filter((court) => {
                // Filter by sport if selected
                if (selectedSport && court.sport_id !== selectedSport.id) {
                  return false;
                }
                return true;
              }) || []
            }
          />
          {selectedEvent && (
            <ReservationDetailsDrawer
              isOpen={!!selectedEvent}
              onClose={() => setSelectedEvent(null)}
              event={selectedEvent}
              users={users}
              courts={club?.courts}
              sports={club?.sports}
              club={club.club}
              fetchData={fetchData}
            />
          )}

          {club?.club?.id && (
            <EventForm
              setShowEventModal={setShowEventModal}
              showEventModal={showEventModal}
              fetchData={fetchData}
              club={club?.club}
              sports={club?.sports}
              courts={club?.courts}
            />
          )}

          {isLoading && <LoadingSpinner />}
        </>
      )}
    </div>
  );
}

// Export with permission wrapper
export default withAdminPermission(
  AdminListDailyScheduler,
  "daily_scheduler",
  "You don't have permission to access daily scheduler"
);
