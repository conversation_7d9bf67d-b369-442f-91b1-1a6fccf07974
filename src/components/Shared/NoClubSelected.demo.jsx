import React from "react";
import NoClubSelected from "./NoClubSelected";

// Demo file showing different usage examples of the NoClubSelected component
// This file is for demonstration purposes only

const NoClubSelectedDemo = () => {
  // Custom icon example
  const CustomClubIcon = () => (
    <svg
      className="h-16 w-16 text-blue-300"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      strokeWidth={1.5}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M2.25 21h19.5m-18-18v18m2.25-18v18m13.5-18v18m2.25-18v18M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.75m-.75 3h.75m-.75 3h.75m-3.75-16.5h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75"
      />
    </svg>
  );

  // Custom action button example
  const refreshButton = (
    <button
      className="inline-flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700 transition-colors"
      onClick={() => window.location.reload()}
    >
      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
      Refresh Page
    </button>
  );

  return (
    <div className="space-y-8 p-8">
      <h1 className="text-3xl font-bold text-gray-900">NoClubSelected Component Demo</h1>
      
      {/* Default Usage */}
      <div className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">Default Usage</h2>
        <div className="h-96 border-2 border-dashed border-gray-200 rounded-lg">
          <NoClubSelected />
        </div>
      </div>

      {/* Custom Title and Description */}
      <div className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">Custom Title and Description</h2>
        <div className="h-96 border-2 border-dashed border-gray-200 rounded-lg">
          <NoClubSelected
            title="Select a Sports Club"
            description="Choose a club from the dropdown menu to access management features, view member information, and configure club settings."
          />
        </div>
      </div>

      {/* Custom Icon */}
      <div className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">Custom Icon</h2>
        <div className="h-96 border-2 border-dashed border-gray-200 rounded-lg">
          <NoClubSelected
            title="Choose Your Club"
            description="Select a club to begin managing reservations, members, and facilities."
            icon={CustomClubIcon}
          />
        </div>
      </div>

      {/* With Action Button */}
      <div className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">With Action Button</h2>
        <div className="h-96 border-2 border-dashed border-gray-200 rounded-lg">
          <NoClubSelected
            title="No Club Available"
            description="It looks like there are no clubs available at the moment. Try refreshing the page or contact support if the issue persists."
            actionButton={refreshButton}
          />
        </div>
      </div>

      {/* Custom Height */}
      <div className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">Custom Height</h2>
        <div className="h-64 border-2 border-dashed border-gray-200 rounded-lg">
          <NoClubSelected
            title="Compact View"
            description="This is a more compact version with custom height."
            height="200px"
          />
        </div>
      </div>

      {/* All Custom Props */}
      <div className="border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">All Custom Props</h2>
        <div className="h-96 border-2 border-dashed border-gray-200 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50">
          <NoClubSelected
            title="Premium Club Selection"
            description="Experience the best club management features. Select a club to unlock advanced analytics, member insights, and premium tools."
            icon={CustomClubIcon}
            height="350px"
            actionButton={refreshButton}
            className="bg-white/50 backdrop-blur-sm rounded-lg mx-4"
          />
        </div>
      </div>
    </div>
  );
};

export default NoClubSelectedDemo;
