import React, { useState, useCallback, useEffect } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "Context/Global";
import Skeleton from "react-loading-skeleton";
import { ModalSidebar } from "Components/ModalSidebar";
import { BiFilterAlt, BiSearch } from "react-icons/bi";
import { AiOutlineClose, AiOutlinePlus } from "react-icons/ai";
import { RiDeleteBin5Line } from "react-icons/ri";
import { FaArrowRight } from "react-icons/fa";
import { HiOutlineTrash } from "react-icons/hi";
// import AddAdminEmailPage from "../Add/AddAdminEmailPage";
// import EditAdminEmailPage from "../Edit/EditAdminEmailPage";
import { AddButton } from "Components/AddButton";
import { PaginationBar } from "Components/PaginationBar";
import _, { set } from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { Link } from "react-router-dom";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { fDateTime } from "Utils/formatTime";
import DeleteModal from "Components/Modals/DeleteModal";
import TreeSDK from "Utils/TreeSDK";
import RightSideModal from "Components/RightSideModal";
import FindABuddyDetails from "Components/FindABuddyDetails";
import ReservationDetailsDrawer from "Components/Drawers/ReservationDetailsDrawer";
import DataTable from "Components/Shared/DataTable";
import HistoryComponent from "Components/HistoryComponent";
import { activityLogTypes } from "Utils/utils";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const columns = [
  {
    header: "Request created",
    accessor: "update_at",
  },
  {
    header: "Looking for",
    accessor: "num_needed",
  },
  {
    header: "Requesting Players",
    accessor: "num_players",
  },
  {
    header: "Requested by",
    accessor: "user_id",
  },
  {
    header: "Sport",
    accessor: "sport_id",
  },
  {
    header: "Court booked",
    accessor: "court_booked",
  },

  // {
  //   header: "",
  //   accessor: "actions",
  // },
];

const ListFindABuddy = ({ sports, club, courts }) => {
  const role = localStorage.getItem("role");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState(null);
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [members, setMembers] = React.useState([]);
  const [showPlayerModal, setShowPlayerModal] = React.useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedBuddy, setSelectedBuddy] = useState(null);

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }
  const fetchMembers = async () => {
    try {
      sdk.setTable("user");
      const response = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club?.id}`, "role,cs,user"],
        },
        "GETALL"
      );
      setMembers(response.list || []);
    } catch (error) {
      console.error("Error fetching members:", error);
      showToast(globalDispatch, "Error fetching members", 3000, "error");
    }
  };
  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      const result = await tdk.getPaginate("buddy", {
        page: pageNum,
        limit: limitNum,
        filter: [...filters, `courtmatchup_reservation.club_id,eq,${club?.id}`],
        join: [
          "sports|sport_id",
          "user|user_id|player_ids",
          "reservation|reservation_id",
        ],
      });

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onStatusSelect = (e) => {
    if (e.target.value === "") {
      getData(1, pageSize);
    } else {
      getData(1, pageSize, {}, [`status,cs,${e.target.value}`]);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "find-a-buddy",
      },
    });
    fetchMembers();
    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [club?.id]);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleDelete = async (id) => {
    setDeleteLoading(true);
    try {
      sdk.setTable("buddy");
      await sdk.callRestAPI({ id }, "DELETE");
      getData(currentPage, pageSize); // Refresh the data
    } catch (error) {
      console.error("Error deleting buddy:", error);
      tokenExpireError(dispatch, error.message);
    } finally {
      setDeleteLoading(false);
    }
  };

  const onSportSelect = (e) => {
    const value = e.target.value;
    if (value === "") {
      getData(1, pageSize);
    } else {
      getData(1, pageSize, {}, [`sport_id,cs,${value}`]);
    }
  };

  const PlayerSelectionModal = () => {
    const filteredMembers = members.filter((member) => {
      const fullName = `${member.first_name || ""} ${
        member.last_name || ""
      }`.toLowerCase();
      return fullName.includes(searchQuery.toLowerCase());
    });

    const toggleMember = (member) => {
      setSelectedPlayer(member);
    };

    return (
      <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50">
        <div className="w-96 rounded-lg bg-white shadow-lg">
          <div className="mb-0 flex items-center justify-between p-4">
            <h3 className="text-lg font-medium">New Find Buddy Request</h3>
            <button
              onClick={() => setIsCoachModalOpen(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
          <h2 className="px-4 text-base font-medium">Request on behalf of:</h2>
          <div className="p-4">
            <div className="mb-4">
              <div className="flex  items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none">
                <span className="w-5">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z"
                      fill="#525866"
                    />
                  </svg>
                </span>
                <input
                  type="text"
                  placeholder="search by name"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full border-none bg-transparent focus:outline-none focus:ring-0"
                />
              </div>
            </div>

            <div className="max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50">
              {filteredMembers.length > 0 &&
                filteredMembers.map((member) => (
                  <div
                    key={member.id}
                    onClick={() => toggleMember(member)}
                    className="flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50"
                  >
                    <input
                      type="radio"
                      checked={selectedPlayer?.id === member.id}
                      onChange={() => toggleMember(member)}
                      className="h-4 w-4 rounded-full border-gray-300 text-blue-600"
                    />
                    <div className="h-8 w-8 overflow-hidden rounded-full bg-gray-200">
                      <img
                        src={member?.photo || "/default-avatar.png"}
                        alt={`${member.first_name} ${member.last_name}`}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <span>{`${member.first_name} ${member.last_name}`}</span>
                  </div>
                ))}
              {!filteredMembers?.length && (
                <p className="text-center text-sm text-gray-500">
                  No members found
                </p>
              )}
            </div>

            <div className="mt-4 flex justify-between gap-3 pt-3">
              <button
                onClick={() => setShowPlayerModal(false)}
                className="flex-1 rounded-xl border border-gray-300 px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (selectedPlayer) {
                    setShowPlayerModal(false);
                    navigate(`/${role}/add-find_a_buddy_requests`, {
                      state: {
                        player: selectedPlayer,
                      },
                    });
                  } else {
                    showToast(
                      globalDispatch,
                      "Please select a member",
                      3000,
                      "error"
                    );
                  }
                }}
                className="flex-1 rounded-xl bg-blue-900 px-4 py-2 text-white hover:bg-blue-800"
              >
                Save and close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const handleSearch = _.debounce((value) => {
    if (value) {
      getData(1, pageSize, {}, [
        `first_name,cs,${value}`,
        `last_name,cs,${value}`,
      ]);
    } else {
      getData(1, pageSize);
    }
  }, 300);

  const handleDateSearch = _.debounce((value) => {
    if (value) {
      getData(1, pageSize, {}, [`date,cs,${value}`]);
    } else {
      getData(1, pageSize);
    }
  }, 300);
  const handleOpenDetails = (buddy) => {
    // Format the buddy data to match what ReservationDetailsDrawer expects
    const formattedBuddy = {
      ...buddy,
      id: buddy?.id,
      date: buddy?.date,
      startTime: buddy?.start_time,
      endTime: buddy?.end_time,
      sport_id: buddy?.sport_id,
      type: buddy?.type,
      sub_type: buddy?.sub_type,
      reservation_type: 2, // Buddy type
      price: buddy?.price,
      status: buddy?.status,
      player_ids: buddy?.player_ids,
      coach_ids: buddy?.coach_ids,
      // Add any other fields needed by the drawer
    };

    setSelectedBuddy(formattedBuddy);
    setShowDetailsModal(true);
  };

  const renderCustomCell = {
    // actions: (row) => (
    //   <div className="flex items-center gap-3">
    //     <button
    //       className="rounded-full p-2 hover:bg-gray-100"
    //       onClick={(e) => {
    //         e.stopPropagation();
    //         handleOpenDetails(row);
    //       }}
    //     >
    //       <svg
    //         width="20"
    //         height="20"
    //         viewBox="0 0 20 20"
    //         fill="none"
    //         xmlns="http://www.w3.org/2000/svg"
    //       >
    //         <path
    //           d="M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993"
    //           stroke="#868C98"
    //           strokeWidth="1.5"
    //           strokeLinecap="round"
    //           strokeLinejoin="round"
    //         />
    //       </svg>
    //     </button>
    //   </div>
    // ),
    user_id: (row) =>
      !row?.user?.first_name
        ? "--"
        : `${row?.user?.first_name} ${row?.user?.last_name}`,
    sport_id: (row) =>
      sports?.find((sport) => sport.id === row?.sport_id)?.name,
    players: (row) => "2 players", // Hardcoded as per original code
    update_at: (row) => fDateTime(row?.update_at),
    court_booked: (row) => (row.reservation_id ? "Yes" : "No"),
  };

  return (
    <div className="h-screen px-8">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-medium leading-6 text-gray-900">
            Find a buddy
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowPlayerModal(true)}
            className="inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
          >
            <span>+</span>
            Add new
          </button>
          <HistoryComponent
            title="Buddy History"
            emptyMessage="No buddy history found"
            activityType={activityLogTypes.find_a_buddy}
          />
        </div>
      </div>
      <div className="flex flex-col justify-between gap-4 py-3 md:flex-row md:items-center">
        <div className="relative flex max-w-sm flex-1 items-center">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <BiSearch className="text-gray-500" />
          </div>
          <input
            type="text"
            onChange={(e) => handleSearch(e.target.value)}
            className="block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="search buddy"
          />
        </div>

        <select
          className="rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
          defaultValue="All"
          onChange={onSportSelect}
        >
          <option value="">Sport: All</option>
          {sports?.map((sport) => (
            <option value={sport.id}>{sport.name}</option>
          ))}
        </select>

        <div className="flex flex-wrap gap-4">
          <input
            type="date"
            className="rounded-md border border-gray-200 text-sm text-gray-500"
            onChange={(e) => handleDateSearch(e.target.value)}
          />
          <input
            type="date"
            className="rounded-md border border-gray-200 text-sm text-gray-500"
            onChange={(e) => handleDateSearch(e.target.value)}
          />

          <select
            className="rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700"
            defaultValue="All"
            onChange={onStatusSelect}
          >
            <option value="">Requesting party:?</option>

            {/* <option value="2">Suspend</option> */}
          </select>
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          renderCustomCell={renderCustomCell}
          rowClassName="hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer"
          emptyMessage="No buddy requests available"
          loadingMessage="Loading buddy requests..."
          onClick={(row) => handleOpenDetails(row)}
        />
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />
      {showPlayerModal && <PlayerSelectionModal />}
      <ReservationDetailsDrawer
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        event={selectedBuddy}
        users={members || []}
        sports={sports || []}
        club={club}
        fetchData={getData}
        courts={courts || []}
      />
      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={handleDelete}
        loading={deleteLoading}
        title="Delete"
        message="Are you sure you want to delete this find a buddy request?"
      />
    </div>
  );
};

export default ListFindABuddy;
