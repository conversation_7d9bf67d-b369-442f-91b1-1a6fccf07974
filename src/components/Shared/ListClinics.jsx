import React from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { PaginationBar } from "Components/PaginationBar";
import _, { set } from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { fDate } from "Utils/formatTime";
import { activityLogTypes, convertTo12Hour } from "Utils/utils";
import RightSideModal from "Components/RightSideModal";
import DeleteModal from "Components/Modals/DeleteModal";
import LoadingSpinner from "Components/LoadingSpinner";
import ClinicDetailsModal from "Components/ClinicDetailsModal/ClinicDetailsModal";
import TreeSDK from "Utils/TreeSDK";
import HistoryComponent from "Components/HistoryComponent";
import DataTable from "Components/Shared/DataTable";
import { BiSearch, BiFilterAlt } from "react-icons/bi";

let sdk = new MkdSDK();
let tdk = new TreeSDK();

const columns = [
  {
    header: "Clinic ID",
    accessor: "id",
  },
  {
    header: "Name",
    accessor: "name",
  },
  {
    header: "Max participants",
    accessor: "max_participants",
  },
  {
    header: "Start Date",
    accessor: "date",
  },
  {
    header: "End date",
    accessor: "end_date",
  },
  {
    header: "Time",
    accessor: "start_time",
  },
  {
    header: "Sport",
    accessor: "sport",
  },
  // {
  //   header: "Age group",
  //   accessor: "age_group",
  // },
  {
    header: "Fee",
    accessor: "cost_per_head",
  },
  // {
  //   header: "Status",
  //   accessor: "status",
  // },
  // {
  //   header: "",
  //   accessor: "actions",
  // },
];

const ListClinics = ({ club, sports, courts }) => {
  const role = localStorage.getItem("role");
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(0);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const [showEditSidebar, setShowEditSidebar] = React.useState(false);
  const [showAddSidebar, setShowAddSidebar] = React.useState(false);
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [showDetailsModal, setShowDetailsModal] = React.useState(false);
  const [selectedClinic, setSelectedClinic] = React.useState(null);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [deletingClinic, setDeletingClinic] = React.useState(false);
  const [deleteLoading, setDeleteLoading] = React.useState(false);
  const [players, setPlayers] = React.useState([]);

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const selectRole = ["", "admin", "employee"];
  const selectStatus = [
    { key: "", value: "All" },
    { key: "0", value: "Inactive" },
    { key: "1", value: "Active" },
    { key: "2", value: "Suspend" },
  ];

  const handleSearch = (value) => {
    if (value === "") {
      getData(1, pageSize);
    } else {
      getData(1, pageSize, {}, [`courtmatchup_clinics.name,cs,${value}`]);
    }
  };

  const handleDateSearch = (date) => {
    console.log("date search", date);
    if (date === "") {
      getData(1, pageSize);
    } else {
      getData(1, pageSize, {}, [`date,cs,${date}`]);
    }
  };

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(showAddSidebar || showEditSidebar ? false : true);
    try {
      sdk.setTable("clinics");

      const result = await tdk.getPaginate("clinics", {
        page: pageNum,
        limit: limitNum,
        filter: [...filters, `courtmatchup_clinics.club_id,eq,${club?.id}`],
        join: ["sports|sport_id"],
      });
      // const result = await tdk.callRestAPI(
      //   {
      //     payload: {
      //       ...data,
      //   },
      //   "PAGINATE"
      // );

      if (result) {
        setLoading(false);
      }
      const { list, total, limit, num_pages, page } = result;

      setCurrentTableData(list);
      setPageSize(limit);
      setPageCount(num_pages);
      setPage(page);
      setDataTotal(total);
      setCanPreviousPage(page > 1);
      setCanNextPage(page + 1 <= num_pages);
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onStatusSelect = (e) => {
    if (e.target.value === "") {
      getData(1, pageSize);
    } else {
      getData(1, pageSize, {}, [`status,cs,${e.target.value}`]);
    }
  };
  const onAgeGroupSelect = (e) => {
    getData(1, pageSize, {}, [`age_group,cs,${e.target.value}`]);
  };

  const onSportSelect = (e) => {
    const value = e.target.value;
    if (value === "") {
      getData(1, pageSize); // Changed from 0 to 1 since page numbers should start at 1
    } else {
      getData(1, pageSize, {}, [`sport_id,eq,${value}`]); // Changed from 0 to 1
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "program-clinics",
      },
    });

    if (!club?.id) {
      return;
    }

    const delay = 700;
    const timeoutId = setTimeout(async () => {
      await getData(1, pageSize);
    }, delay);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [club]);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const clearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    getData(1, pageSize);
  };

  const handleDelete = async (id) => {
    setDeletingClinic(true);
    try {
      sdk.setTable("clinics");
      await sdk.callRestAPI({ id }, "DELETE");
      getData(currentPage, pageSize); // Refresh the data
    } catch (error) {
      console.error("Error deleting clinic:", error);
      tokenExpireError(dispatch, error.message);
    } finally {
      setDeletingClinic(false);
    }
  };

  const handleOpenDetails = (clinic) => {
    // Format the clinic data to match what ReservationDetailsDrawer expects
    const formattedClinic = {
      ...clinic,
      id: clinic?.id,
      date: clinic?.date,
      startTime: clinic?.start_time,
      endTime: clinic?.end_time,
      sport_id: clinic?.sport_id,
      type: clinic?.type,
      sub_type: clinic?.sub_type,
      reservation_type: 3, // Clinic type
      price: clinic?.price,
      status: clinic?.status,
      player_ids: clinic?.player_ids,
      coach_ids: clinic?.coach_ids,
      // Add any other fields needed by the drawer
    };

    setSelectedClinic(formattedClinic);
    setShowDetailsModal(true);
  };

  const renderCustomCell = {
    // actions: (row) => (
    //   <div className="flex items-center gap-3">
    //     <button
    //       className="rounded-full p-2 hover:bg-gray-100"
    //       onClick={(e) => {
    //         e.stopPropagation();
    //         handleOpenDetails(row);
    //       }}
    //     >
    //       <svg
    //         width="20"
    //         height="20"
    //         viewBox="0 0 20 20"
    //         fill="none"
    //         xmlns="http://www.w3.org/2000/svg"
    //       >
    //         <path
    //           d="M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993"
    //           stroke="#868C98"
    //           strokeWidth="1.5"
    //           strokeLinecap="round"
    //           strokeLinejoin="round"
    //         />
    //       </svg>
    //     </button>
    //   </div>
    // ),
    status: (row) => (
      <span
        className={`rounded-lg px-3 py-1 text-sm ${
          row.status === 1
            ? "bg-[#D1FAE5] text-[#065F46]"
            : "bg-[#F4F4F4] text-[#393939]"
        }`}
      >
        {row.status === 1 ? "Active" : "Inactive"}
      </span>
    ),
    start_time: (row) => convertTo12Hour(row?.start_time),
    date: (row) => fDate(row?.date),
    end_date: (row) => fDate(row?.end_date),
    players: (row) =>
      row?.player_ids
        ? `${JSON.parse(row?.player_ids).length} players`
        : "0 players",
    sport: (row) => row?.sports?.name,
  };

  return (
    <div className="h-screen px-2 md:px-8">
      {deletingClinic || (loading && <LoadingSpinner />)}
      <div className="flex flex-col gap-4 py-3">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="relative flex w-full max-w-xs flex-1 items-center">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <BiSearch className="text-gray-500" />
              </div>
              <input
                type="text"
                className="block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="search clinics"
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
            <input
              type="date"
              className="w-full rounded-md border border-gray-200 text-sm text-gray-500 sm:w-auto"
              onChange={(e) => handleDateSearch(e.target.value)}
            />
          </div>

          <div className="flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center">
            <select
              className="w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto"
              defaultValue="All"
              onChange={onSportSelect}
            >
              <option value="">Sport: All</option>
              {sports?.map((sport) => (
                <option key={sport.id} value={sport.id}>
                  {sport.name}
                </option>
              ))}
            </select>

            <select
              className="w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm text-gray-700 sm:w-auto"
              defaultValue="All"
              onChange={onStatusSelect}
            >
              <option value="">Status: All</option>
              <option value="0">Inactive</option>
              <option value="1">Active</option>
              {/* <option value="2">Suspend</option> */}
            </select>

            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate(`/${role}/program-clinics/add`)}
                className="inline-flex items-center gap-2 rounded-md bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
              >
                <span>+</span>
                Add new
              </button>
              <HistoryComponent
                title="Clinic History"
                emptyMessage="No clinic history found"
                activityType={activityLogTypes.clinic}
              />
            </div>
          </div>
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          loading={loading}
          renderCustomCell={renderCustomCell}
          rowClassName="hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer"
          emptyMessage="No clinics available"
          loadingMessage="Loading clinics..."
          onClick={(row) => handleOpenDetails(row)}
        />
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />

      {/* <ReservationDetailsDrawer
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        event={selectedClinic}
        users={players || []}
        sports={sports || []}
        club={club}
        fetchData={getData}
        courts={courts || []}
      /> */}

      <DeleteModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={handleDelete}
        loading={deleteLoading}
        title="Delete"
        message="Are you sure you want to delete this clinic?"
      />

      <ClinicDetailsModal
        getData={getData}
        onClose={() => setSelectedClinic(null)}
        clinic={selectedClinic}
        isOpen={selectedClinic !== null}
        sports={sports}
      />
    </div>
  );
};

export default ListClinics;
