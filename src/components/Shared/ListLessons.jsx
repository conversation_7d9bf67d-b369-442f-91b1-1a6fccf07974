import React, { useState } from "react";
import { AuthContext, tokenExpireError } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { GlobalContext } from "Context/Global";
import * as yup from "yup";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { BiSearch } from "react-icons/bi";
import { PaginationBar } from "Components/PaginationBar";
import _ from "lodash";
import SkeletonLoader from "Components/Skeleton/Skeleton";
import { fDate, fTimeSuffix } from "Utils/formatTime";
import { fCurrency } from "Utils/formatNumber";
import ReservationDetailsDrawer from "Components/Drawers/ReservationDetailsDrawer";
import AddReservationDrawer from "Components/Reservations/AddReservationDrawer";
import LoadingOverlay from "Components/Loading/LoadingOverlay";
import {
  BOOKING_STATUSES,
  calculateReservationTimeLeft,
  eventTypeOptions,
  reservationTypes,
} from "Utils/utils";
import TreeSDK from "Utils/TreeSDK";
import {
  FailedStatus,
  PaidStatus,
  ReservedStatus,
  TimeStatus,
} from "Components/ReservationStatus";
import CheckinModal from "Components/CheckinModal";
import CheckoutModal from "Components/CheckoutModal";
import DataTable from "./DataTable";
import LessonDetailModal from "Components/LessonDetailModal";
import HistoryComponent from "Components/HistoryComponent";
import AddLessonDrawer from "Components/Reservations/AddLessonDrawer";
let sdk = new MkdSDK();
let tdk = new TreeSDK();
const columns = [
  {
    header: "Date",
    accessor: "date",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "By",
    accessor: "user",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Sport",
    accessor: "sport",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },
  {
    header: "Type",
    accessor: "type",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    // mappings: { 1: "Indoor", 0: "Outdoor" },
  },

  // {
  //   header: "Event",
  //   accessor: "event",
  //   isSorted: false,
  //   isSortedDesc: false,
  //   mappingExist: false,
  //   mappings: {},
  // },

  {
    header: "Create At",
    accessor: "create_at",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: {},
  },

  {
    header: "Status",
    accessor: "status",
    isSorted: false,
    isSortedDesc: false,
    mappingExist: false,
    mappings: { 1: "Paid", 0: "Reserved", 2: "Failed" },
  },
  // {
  //   header: "Action",
  //   accessor: "",
  // },
];

const ListLessons = ({ sports, club, courts }) => {
  const { dispatch: globalDispatch, state: globalState } =
    React.useContext(GlobalContext);
  const { dispatch } = React.useContext(AuthContext);
  const [data, setCurrentTableData] = React.useState([]);
  const [pageSize, setPageSize] = React.useState(10);
  const [pageCount, setPageCount] = React.useState(0);
  const [dataTotal, setDataTotal] = React.useState(0);
  const [currentPage, setPage] = React.useState(1);
  const [canPreviousPage, setCanPreviousPage] = React.useState(false);
  const [canNextPage, setCanNextPage] = React.useState(false);
  const [openFilter, setOpenFilter] = React.useState(false);
  const [selectedOptions, setSelectedOptions] = React.useState([]);
  const [filterConditions, setFilterConditions] = React.useState([]);
  const [loading, setLoading] = React.useState(true);
  const navigate = useNavigate();
  const dropdownFilterRef = React.useRef(null);
  const [showDetailsModal, setShowDetailsModal] = React.useState(false);
  const [selectedReservation, setSelectedReservation] = React.useState(null);
  const [showAddDrawer, setShowAddDrawer] = useState(false);
  const [showCheckin, setShowCheckin] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);

  const [surfaces, setSurfaces] = useState([]);
  const [players, setPlayers] = useState([]);

  const schema = yup.object({
    id: yup.string(),
    email: yup.string(),
    role: yup.string(),
    status: yup.string(),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  function previousPage() {
    getData(currentPage - 1, pageSize);
  }

  function nextPage() {
    getData(currentPage + 1, pageSize);
  }

  const fetchPlayers = async () => {
    try {
      sdk.setTable("user");
      const response = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club?.id}`, "role,cs,user"],
        },
        "GETALL"
      );
      setPlayers(response.list || []);
    } catch (error) {
      console.error("Error fetching players:", error);
      showToast(globalDispatch, "Error fetching players", 3000, "error");
    }
  };

  async function getData(pageNum, limitNum, data = {}, filters = []) {
    setLoading(true);
    try {
      const result = await tdk.getPaginate("reservation", {
        page: pageNum,
        limit: limitNum,
        filter: [
          ...filters,
          `courtmatchup_reservation.club_id,cs,${club?.id}`,
          `courtmatchup_booking.reservation_type,cs,${reservationTypes.lesson}`,
        ],
        join: ["clubs|club_id", "booking|booking_id", "user|user_id"],
        size: pageSize,
      });
      const bookingList = result.list;
      if (result) {
        // Merge booking list with user and sport data
        const mergedList = bookingList.map((booking) => {
          const sport = sports.find((sport) => sport.id === booking.sport_id);
          return {
            ...booking,
            sport: sport ? sport.name : "--", // Add sport name from sportList
          };
        });

        setLoading(false);
        setCurrentTableData(mergedList);
        setPageSize(result.limit);
        setPageCount(result.num_pages);
        setPage(result.page);
        setDataTotal(result.total);
        setCanPreviousPage(result.page > 1);
        setCanNextPage(result.page + 1 <= result.num_pages);
      }
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(dispatch, error.message);
    }
  }

  const onSubmit = (data) => {
    if (data.search) {
      getData(1, pageSize, {}, [
        `first_name,cs,${data.search}`,
        `last_name,cs,${data.search}`,
      ]);
    } else {
      getData(1, pageSize);
    }
  };

  const onSportSelect = async (e) => {
    const value = e.target.value;
    if (value === "") {
      await getData(1, pageSize);
    } else {
      await getData(1, pageSize, {}, [`sport_id,eq,${parseInt(value)}`]);
    }
  };

  const onReservationTypeSelect = async (e) => {
    const value = e.target.value;
    if (value === "") {
      await getData(currentPage, pageSize);
    } else {
      await getData(currentPage, pageSize, {}, [
        `courtmatchup_booking.reservation_type,cs,${e.target.value}`,
      ]);
    }
  };

  const onStatusSelect = async (e) => {
    const value = e.target.value;
    if (value === "") {
      await getData(currentPage, pageSize);
    } else {
      await getData(currentPage, pageSize, {}, [
        `courtmatchup_booking.status,cs,${e.target.value}`,
      ]);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "lessons",
      },
    });

    if (club?.id) {
      getData(1, pageSize, {});
      fetchPlayers();
    }
  }, [club?.id]);

  const handleClickOutside = (event) => {
    if (
      dropdownFilterRef.current &&
      !dropdownFilterRef.current.contains(event.target)
    ) {
      setOpenFilter(false);
    }
  };

  React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const clearAllFilters = () => {
    setSelectedOptions([]);
    setFilterConditions([]);
    getData(1, pageSize);
  };

  const handleDelete = async (id) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      try {
        sdk.setTable("reservations");
        await sdk.callRestAPI({ id }, "DELETE");
        getData(currentPage, pageSize); // Refresh the data
      } catch (error) {
        console.error("Error deleting user:", error);
        tokenExpireError(dispatch, error.message);
      }
    }
  };

  const handleOpenDetails = (reservation) => {
    // Format the reservation data to match what ReservationDetailsDrawer expects
    const formattedReservation = {
      ...reservation,
      id: reservation.booking?.id,
      date: reservation.booking?.date,
      startTime: reservation.booking?.start_time,
      endTime: reservation.booking?.end_time,
      sport_id: reservation.booking?.sport_id,
      type: reservation.booking?.type,
      sub_type: reservation.booking?.subtype,
      reservation_type: reservation.booking?.reservation_type,
      price: reservation.booking?.price,
      status: reservation.booking?.status,
      player_ids: reservation.booking?.player_ids,
      coach_ids: reservation.booking?.coach_ids,
      // Add any other fields needed by the drawer
    };

    setSelectedReservation(formattedReservation);
    setShowDetailsModal(true);
  };

  const renderCustomCell = {
    // "": (row) => (
    //   <div className="flex items-center gap-3">
    //     <button
    //       className="rounded-full p-2 hover:bg-gray-100"
    //       onClick={(e) => {
    //         e.stopPropagation();
    //         handleOpenDetails(row);
    //       }}
    //     >
    //       <svg
    //         width="20"
    //         height="20"
    //         viewBox="0 0 20 20"
    //         fill="none"
    //         xmlns="http://www.w3.org/2000/svg"
    //       >
    //         <path
    //           d="M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993"
    //           stroke="#868C98"
    //           strokeWidth="1.5"
    //           strokeLinecap="round"
    //           strokeLinejoin="round"
    //         />
    //       </svg>
    //     </button>
    //   </div>
    // ),
    type: (row) => (
      <span className="capitalize">
        {eventTypeOptions.find(
          (option) => option.value == row?.booking?.reservation_type
        )?.label || "--"}
      </span>
    ),
    sport: (row) => (
      <span className="capitalize">
        {sports.find((sport) => sport.id === row?.booking?.sport_id)?.name ||
          "--"}
      </span>
    ),
    date: (row) => (
      <>
        {fDate(row?.booking?.date)} {" | "}{" "}
        {fTimeSuffix(row?.booking?.start_time)} {" - "}{" "}
        {fTimeSuffix(row?.booking?.end_time)}
      </>
    ),
    players: (row) => (
      <>
        {row?.booking?.player_ids
          ? `${JSON.parse(row?.booking?.player_ids).length} players`
          : "0 players"}
      </>
    ),
    bill: (row) => <>{fCurrency(row?.booking?.price)}</>,
    user: (row) => (
      <>{`${row?.user?.first_name || ""} ${row?.user?.last_name || ""}`}</>
    ),
    status: (row) => (
      <>
        {row.booking.status == BOOKING_STATUSES.SUCCESS && <PaidStatus />}
        {row.booking.status == BOOKING_STATUSES.PENDING && (
          <div className="flex items-center gap-1">
            <ReservedStatus />
            <TimeStatus
              timeLeft={calculateReservationTimeLeft(
                row?.reservation_updated_at
              )}
            />
          </div>
        )}
        {row.booking.status == BOOKING_STATUSES.FAIL && <FailedStatus />}
      </>
    ),
  };

  return (
    <div className="h-screen px-2 md:px-8">
      {loading && <LoadingOverlay />}
      <div className="flex flex-col gap-4 py-3">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <form
              className="relative flex flex-1 items-center"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <BiSearch className="text-gray-500" />
              </div>
              <input
                type="text"
                className="block w-full rounded-lg border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="search users"
                {...register("search")}
              />
            </form>
            <div className="flex flex-col gap-2 xs:flex-row xs:gap-4">
              <input
                type="date"
                className="w-full rounded-lg border border-gray-200 text-sm text-gray-500 xs:w-auto"
              />
              <input
                type="time"
                defaultValue="00:00"
                className="w-full rounded-lg border border-gray-200 text-sm text-gray-500 xs:w-auto"
              />
            </div>
          </div>

          <div className="flex flex-col gap-4 sm:flex-row sm:flex-wrap sm:items-center">
            <select
              className="w-full rounded-md border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 sm:w-auto"
              defaultValue="Sport: All"
              onChange={onSportSelect}
            >
              <option value="">Sport: All</option>
              {sports?.map((sport) => (
                <option key={sport.id} value={sport.id}>
                  {sport.name}
                </option>
              ))}
            </select>

            {/* <select
              className="w-full rounded-lg border border-gray-200 px-3 py-2 pr-8 text-sm capitalize text-gray-700 sm:w-auto"
              defaultValue="All"
              onChange={onReservationTypeSelect}
            >
              <option value="">Reservation Type: All</option>
              {eventTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select> */}

            <div className="flex items-center gap-4">
              <button
                onClick={() => setShowAddDrawer(true)}
                className="inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700"
              >
                <span>+</span>
                Add new
              </button>

              <HistoryComponent
                title="Lesson History"
                emptyMessage="No lesson history found"
              />
            </div>
          </div>
        </div>
      </div>
      {loading ? (
        <SkeletonLoader />
      ) : (
        <div className="overflow-x-auto">
          <DataTable
            columns={columns}
            data={data}
            loading={loading}
            renderCustomCell={renderCustomCell}
            rowClassName="hover:bg-gray-40 bg-gray-100 px-4 py-3 text-gray-500 cursor-pointer"
            cellClassName="whitespace-nowrap px-6 py-4"
            headerClassName="px-6 py-4 text-left text-sm font-medium text-gray-500"
            emptyMessage="No data available"
            onClick={(row) => handleOpenDetails(row)}
          />
        </div>
      )}
      <PaginationBar
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        canPreviousPage={canPreviousPage}
        canNextPage={canNextPage}
        updatePageSize={(newPageSize) => {
          setPageSize(newPageSize);
          getData(1, newPageSize);
        }}
        previousPage={previousPage}
        nextPage={nextPage}
        gotoPage={(pageNum) => getData(pageNum, pageSize)}
      />

      <ReservationDetailsDrawer
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        event={selectedReservation}
        users={players}
        sports={sports}
        club={club}
        fetchData={getData}
        courts={courts}
      />

      <AddLessonDrawer
        isOpen={showAddDrawer}
        club={club}
        onClose={() => setShowAddDrawer(false)}
        sports={sports}
        players={players}
      />
      {showCheckin && (
        <CheckinModal
          courts={courts}
          onClose={() => setShowCheckin(false)}
          reservation={selectedReservation}
          getData={getData}
          sports={sports}
          setReservation={setSelectedReservation}
        />
      )}
      {showCheckout && (
        <CheckoutModal
          reservation={selectedReservation}
          onClose={() => setShowCheckout(false)}
          getData={getData}
          setReservation={setSelectedReservation}
        />
      )}
    </div>
  );
};

export default ListLessons;
