import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,L as J,b as p}from"./vendor-851db8c1.js";import{aV as _,aW as q,aX as G,p as $,aY as U,aZ as K,a_ as Q,a2 as S,a$ as Y,a3 as V,b0 as E,b1 as B,M as X,b2 as ee}from"./index-3b44b02c.js";import{P as M}from"./@fortawesome/react-fontawesome-13437837.js";import"./react-slick-6a88bba0.js";import{u as se,G as te,M as ae,I as le}from"./@react-google-maps/api-bec1613d.js";import{S as ie}from"./react-select-c8303602.js";import{P as ne}from"./PublicFAQ-ab4cc619.js";import{u as re}from"./react-intersection-observer-1e72ff12.js";import{A as T}from"./aos-65d9ceb8.js";import{m as x}from"./framer-motion-c6ba3e69.js";import"./@craftjs/core-d3c11b68.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";const R=({icon:d,iconSize:f,title:w,description:a})=>{const[N,o]=r.useState(window.innerWidth<640);return r.useEffect(()=>{const j=()=>{o(window.innerWidth<640)};return window.addEventListener("resize",j),()=>window.removeEventListener("resize",j)},[]),e.jsxs("div",{className:"relative rounded-2xl bg-[#C2BD9F] p-4 sm:p-6",children:[e.jsxs("div",{className:"relative z-10 pt-6 sm:pt-4",children:[e.jsx("div",{className:"absolute -top-10 right-4 flex h-10 w-10 items-center justify-center rounded-full bg-white text-[#005954] shadow-md sm:-top-16 sm:right-7 sm:h-14 sm:w-14",children:typeof d=="function"&&!f?e.jsx(d,{className:"h-4 w-4 sm:h-6 sm:w-6"}):e.jsx(d,{size:f||(N?16:24)})}),e.jsx("h3",{className:"font-ubuntu mb-2 pr-12 text-base font-bold sm:mb-3 sm:pr-16 sm:text-xl",children:w}),e.jsx("p",{className:"text-xs text-gray-700 sm:text-base",children:a})]}),e.jsx("img",{src:"/images/landing/feature-bg-pattern.png",alt:"Tennis Racket",className:"absolute right-0 top-0 z-0 h-full w-auto max-w-[80%] opacity-50 sm:max-w-full sm:opacity-70 md:opacity-100",loading:"lazy"})]})};R.propTypes={icon:M.oneOfType([M.func,M.object]).isRequired,iconSize:M.number,title:M.string.isRequired,description:M.string.isRequired};const oe=R,de=[{id:1,icon:_,iconSize:20,title:"Automated Court Scheduling",description:"Avoid double bookings and optimize court usage with our intelligent scheduling system."},{id:2,icon:q,iconSize:20,title:"Coach Availability Management",description:"Let coaches set their own schedules and accept lesson requests directly through the platform."},{id:3,icon:G,iconSize:20,title:"Player Matching",description:"Help members find ideal hitting partners based on skill level and availability."},{id:4,icon:$,iconSize:20,title:"Integrated Payments",description:"Secure, low-fee transactions built directly into your system for seamless booking experience."},{id:5,icon:U,iconSize:20,title:"Event Creation Tools",description:"Host clinics, round robins, and lessons with ease using our intuitive event management tools."},{id:6,icon:K,iconSize:20,title:"Performance Reporting",description:"Get valuable insights on court usage, bookings, and revenue to optimize your operations."}],ce="/assets/GenericMarker-ce4bdde5.svg",me="AIzaSyC6zItKyKbnIcdpgwNoRIByQEvezUbdFAA",xe=["places"],he=d=>({width:"100%",height:d?"400px":"500px",borderRadius:"12px"}),F={lat:37.0902,lng:-95.7129},ue=({clubs:d})=>{const{isLoaded:f,loadError:w}=se({googleMapsApiKey:me,libraries:xe}),[a,N]=r.useState(null),[o,j]=r.useState(""),[k,b]=r.useState([]),[z,v]=r.useState(F),[c,t]=r.useState(null),[l,h]=r.useState(window.innerWidth<768),[m,y]=r.useState([]);r.useEffect(()=>{const s=()=>{h(window.innerWidth<768)};return window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[]);const g=r.useMemo(()=>d.filter(s=>{try{if(!s.club_location)return!1;const i=JSON.parse(s.club_location);return i&&i.lat&&i.lng}catch(i){return console.error("Error parsing club location:",i),!1}}),[d]),n=r.useMemo(()=>{const s=new Set;return d.forEach(i=>{i.sports&&Array.isArray(i.sports)&&i.sports.forEach(u=>{u.name&&u.name.trim()!==""&&s.add(u.name.trim())})}),Array.from(s).sort().map(i=>({value:i,label:i}))},[d]);r.useEffect(()=>{b(g),v(F)},[g]);const C=r.useCallback(s=>{t(s)},[]),L=r.useMemo(()=>{let s=g;if(o.trim()){const i=o.toLowerCase();s=s.filter(u=>u.name.toLowerCase().includes(i)||u.address&&u.address.toLowerCase().includes(i))}if(m.length>0){const i=m.map(u=>u.value);s=s.filter(u=>!u.sports||!Array.isArray(u.sports)?!1:u.sports.some(I=>i.includes(I.name)))}return s},[g,o,m]);r.useEffect(()=>{b(L)},[L]),r.useEffect(()=>{if(c)if(L.length>0)try{const s=JSON.parse(L[0].club_location);(o.trim()||m.length>0)&&(c.panTo({lat:s.lat,lng:s.lng}),c.setZoom(10))}catch(s){console.error("Error centering map on search result:",s),c.panTo(F),c.setZoom(4)}else(o.trim()||m.length>0)&&(c.panTo(F),c.setZoom(4))},[L,c,o,m]);const H=r.useCallback(()=>{},[]),Z=r.useCallback(s=>{y(s||[])},[]),A=s=>{N(s)},P=()=>{N(null)},O=r.useCallback(()=>{c&&(c.panTo(F),c.setZoom(4))},[c]),D=r.useMemo(()=>({control:(s,i)=>({...s,borderColor:i.isFocused?"#005954":"#C6C6C6",borderRadius:"0.75rem",backgroundColor:"#F8F8F8",minHeight:l?"38px":"44px",boxShadow:i.isFocused?"0 0 0 1px #005954":"none","&:hover":{borderColor:"#005954"}}),multiValue:s=>({...s,backgroundColor:"#005954",borderRadius:"0.375rem"}),multiValueLabel:s=>({...s,color:"white",fontSize:l?"0.75rem":"0.875rem"}),multiValueRemove:s=>({...s,color:"white","&:hover":{backgroundColor:"#004540",color:"white"}}),placeholder:s=>({...s,color:"#6B7280",fontSize:l?"0.875rem":"1rem"})}),[l]);return w?e.jsx("div",{className:"py-8 text-center",children:"Error loading maps"}):f?e.jsxs("div",{className:"club-map-container",children:[e.jsxs("div",{className:"relative mb-4 space-y-3",children:[e.jsxs("div",{className:"flex",children:[e.jsx("input",{type:"text",placeholder:l?"Search clubs...":"Search clubs by name or location",className:"w-full rounded-l-xl border-[1px] border-[#C6C6C6] bg-[#F8F8F8] p-2 px-3 py-2 text-sm leading-tight text-gray-700 focus:border-[#005954] focus:outline-none focus:ring-1 focus:ring-[#005954] sm:px-4 sm:py-3 sm:text-base",value:o,onChange:s=>j(s.target.value),onKeyDown:s=>s.key==="Enter"&&H()}),e.jsx("button",{onClick:H,className:"flex items-center justify-center rounded-r-xl bg-[#005954] px-3 text-white hover:bg-[#004540] sm:px-4",children:e.jsx(Q,{className:"h-3 w-3 sm:h-4 sm:w-4"})})]}),e.jsx("div",{className:"w-full",children:e.jsx(ie,{isMulti:!0,options:n,value:m,onChange:Z,placeholder:l?"Filter by sports...":"Filter by sports",className:"text-sm sm:text-base",classNamePrefix:"react-select",styles:D,noOptionsMessage:()=>"No sports found"})})]}),g.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 p-8 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-gray-200 p-4",children:e.jsx(S,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h3",{className:"mb-2 text-xl font-bold text-gray-700",children:"No Clubs Found"}),e.jsx("p",{className:"text-gray-500",children:"There are no clubs with location data available at this time."})]}):e.jsxs(te,{mapContainerStyle:he(l),center:z,zoom:4,onLoad:C,options:{fullscreenControl:!0,streetViewControl:!l,mapTypeControl:!l,zoomControl:!0},children:[e.jsx("div",{className:"absolute right-2 top-2 z-10 cursor-pointer rounded-full bg-white p-2 shadow-md hover:bg-gray-100",onClick:O,title:"Reset to US View",children:e.jsx(Y,{className:"h-5 w-5 text-[#005954]"})}),k.map(s=>{try{const i=JSON.parse(s.club_location);return e.jsx(ae,{position:{lat:i.lat,lng:i.lng},onClick:()=>A(s),icon:{url:ce,scaledSize:new window.google.maps.Size(40,60),anchor:new window.google.maps.Point(20,60)}},s.id)}catch(i){return console.error("Error rendering marker:",i),null}}),a&&e.jsx(le,{position:JSON.parse(a.club_location),onCloseClick:P,children:e.jsxs("div",{className:"club-info max-w-xs p-2",children:[e.jsx("h3",{className:"mb-1 text-base font-bold sm:text-lg",children:a.name}),JSON.parse(a.club_location).address&&e.jsxs("p",{className:"mb-2 flex items-start text-xs sm:text-sm",children:[e.jsx(S,{className:"mr-1 mt-0.5 h-3 w-3 text-[#005954] sm:mt-1 sm:h-4 sm:w-4"}),e.jsx("span",{children:JSON.parse(a.club_location).address})]}),e.jsxs("div",{className:"my-2 space-y-1 text-xs sm:text-sm",children:[(()=>{try{if(a.splash_screen){const s=JSON.parse(a.splash_screen);if(s.bio)return e.jsx("p",{className:"mb-1 line-clamp-2",children:s.bio})}return a.bio?e.jsx("p",{className:"mb-1 line-clamp-2",children:a.bio}):null}catch(s){return console.error("Error parsing splash screen data:",s),a.bio?e.jsx("p",{className:"mb-1 line-clamp-2",children:a.bio}):null}})(),a.times&&e.jsxs("p",{children:[e.jsx("strong",{children:"Hours:"})," ",(()=>{try{const s=JSON.parse(a.times);return s.length>0?s.map((i,u)=>`${V(i.from.substring(0,5))} - ${V(i.until.substring(0,5))}${u<s.length-1?", ":""}`).join(""):"Contact club for hours"}catch(s){return console.error("Error parsing club times:",s),"Contact club for hours"}})()]}),a.membership_settings&&e.jsxs("p",{children:[e.jsx("strong",{children:"Plans:"})," ",(()=>{try{const s=JSON.parse(a.membership_settings);return s.length>0?`${s.length} membership plans available`:"No membership plans"}catch{return"Membership plans available"}})()]}),a.days_off&&e.jsxs("p",{children:[e.jsx("strong",{children:"Closed on:"})," ",(()=>{try{const s=JSON.parse(a.days_off);return s.length>0?s.join(", "):"Open all week"}catch{return"Contact club for details"}})()]})]}),e.jsxs("div",{className:"mt-3 flex gap-2",children:[e.jsx(J,{to:`/club/${a.id}`,className:"flex-1 rounded-lg bg-[#005954] px-3 py-1.5 text-center text-xs text-white transition-colors hover:bg-[#004540] sm:px-4 sm:py-2 sm:text-sm",children:"Visit Club"}),a.club_logo&&e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full border border-gray-200 sm:h-10 sm:w-10",children:e.jsx("img",{src:a.club_logo,alt:`${a.name} logo`,className:"h-full w-full object-cover",loading:"lazy"})})]})]})})]})]}):e.jsx("div",{className:"py-8 text-center",children:"Loading maps..."})},pe=ue;let W=new X;function ts(){r.useRef(null);const[d,f]=p.useState([0,0,0]),[w,a]=p.useState([]),[N,o]=p.useState(!0),[j,k]=p.useState(window.innerWidth),b={bio:"Welcome to Court Matchup - the ultimate platform for tennis enthusiasts. Book courts, find partners, join clinics, and more!",images:[{url:"/images/landing/tennis-player.jpg",isDefault:!0,id:"default-1",type:"image"},{url:"/images/landing/male-tennis-player.jpg",isDefault:!0,id:"default-2",type:"image"},{url:"/images/landing/female-tennis-player.jpg",isDefault:!0,id:"default-3",type:"image"},{url:"/images/landing/male-tennis-player2.jpg",isDefault:!0,id:"default-4",type:"image"},{url:"/images/landing/male-tennis-player3.jpg",isDefault:!0,id:"default-5",type:"image"},{url:"/images/landing/racket-with-tennis-ball.jpg",isDefault:!0,id:"default-6",type:"image"}],slideshow_delay:5e3},z=[{id:1,name:"Sarah Williams",image:"/images/landing/female-tennis-player.jpg",bio:"Former WTA professional with over 15 years of coaching experience specializing in technical development and match strategy.",club:"Grand Slam Academy",specialties:["Adult Training","Junior Development","Tournament Prep"]},{id:2,name:"Michael Chen",image:"/images/landing/male-tennis-player3.jpg",bio:"USPTA Elite Professional with expertise in modern playing techniques and video analysis for players of all levels.",club:"Riverside Tennis Club",specialties:["Video Analysis","Serve Development","Footwork"]},{id:3,name:"David Thompson",image:"/images/landing/male-tennis-player.jpg",bio:"Former Division I collegiate player with a focus on junior development and high-performance training programs.",club:"Ace Tennis Club",specialties:["Junior Elite","College Prep","Fitness Training"]},{id:4,name:"Jennifer Rodriguez",image:"/images/landing/female-tennis-player.jpg",bio:"Certified PTR Professional specializing in beginner to intermediate players and adaptive tennis programs.",club:"City Sports Complex",specialties:["Beginners","Adaptive Tennis","Group Clinics"]}],v=async()=>{o(!0);try{const t=await W.callRawAPI("/v3/api/custom/courtmatchup/users/clubs",{},"GET");a(t.clubs)}catch(t){console.log(t)}o(!1)};p.useEffect(()=>{v()},[]),r.useEffect(()=>{T.init({duration:600,once:!0,mirror:!1,offset:50,easing:"ease-out",disable:"mobile"})},[]),r.useEffect(()=>{let t=null;const l=()=>{t&&clearTimeout(t),t=setTimeout(()=>{k(window.innerWidth),T.refresh()},150)};return window.addEventListener("resize",l),()=>{window.removeEventListener("resize",l),t&&clearTimeout(t)}},[]);const c=p.useMemo(()=>{const t=b.images||[],l=[[],[],[]];return t.forEach((h,m)=>{if(h!=null&&h.url){const y=Math.floor(m/3);y<3&&l[y].push({url:h==null?void 0:h.url})}}),l},[]);return p.useEffect(()=>{let t=!1,l;const h=()=>{t=!0,clearTimeout(l),l=setTimeout(()=>{t=!1},150)};window.addEventListener("scroll",h,{passive:!0});const m=setInterval(()=>{t||f(y=>{const g=[...y];return c.forEach((n,C)=>{n.length>1&&(g[C]=(g[C]+1)%n.length)}),g})},b.slideshow_delay);return()=>{clearInterval(m),window.removeEventListener("scroll",h),clearTimeout(l)}},[c,b.slideshow_delay]),p.useEffect(()=>{(async()=>{o(!0);try{const l=await W.callRawAPI("/v3/api/custom/courtmatchup/users/clubs",{},"GET");a(l.clubs||[])}catch(l){console.error("Error fetching clubs:",l),a([])}finally{o(!1)}})()},[]),e.jsxs("div",{className:"flex min-h-screen flex-col",children:[e.jsxs("section",{className:"relative min-h-screen overflow-hidden bg-[#005954]",children:[e.jsx("div",{className:"absolute inset-0 z-10","data-aos":"fade-in","data-aos-duration":"1500",children:e.jsx("img",{src:"/images/landing/tennis-player.jpg",alt:"Tennis Player",className:"h-full w-full object-cover",loading:"lazy"})}),e.jsx("div",{className:"absolute inset-0 z-20 bg-[#005954] opacity-40"}),e.jsx("div",{className:"container relative z-30 mx-auto flex h-full min-h-screen max-w-7xl flex-col justify-center px-4 py-16 sm:px-6 md:py-32",children:e.jsxs("div",{className:"max-w-2xl",children:[e.jsx(x.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"font-ubuntu mb-3 text-sm uppercase tracking-wider text-white sm:mb-4 sm:text-base",children:"Welcome to our company"}),e.jsxs(x.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"font-ubuntu mb-3 text-3xl font-bold leading-tight text-white sm:mb-4 sm:text-4xl md:text-5xl lg:text-6xl",children:["The following ",e.jsx("span",{className:"font-normal",children:"decades"})," were a"," ",e.jsx("span",{className:"block sm:inline",children:"legendary era."})]}),e.jsxs(x.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"font-outfit mb-6 text-sm text-white sm:mb-8 sm:text-base",children:["Sophie Moore, Our Head Waiter, Makes Sure Your Evening Is"," ",e.jsx("span",{className:"block sm:inline",children:"The Best It Can Be. Their Vast Culinary Knowledge,"})," ",e.jsx("span",{className:"block sm:inline",children:"Precise Timing, And Gracious Manner."})]}),e.jsxs(x.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"relative mb-8 max-w-md rounded-2xl bg-white shadow-lg",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3 sm:pl-4",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-6 sm:w-6",children:[e.jsx("path",{d:"M17 21.1504H7C4 21.1504 2 19.6504 2 16.1504V9.15039C2 5.65039 4 4.15039 7 4.15039H17C20 4.15039 22 5.65039 22 9.15039V16.1504C22 19.6504 20 21.1504 17 21.1504Z",stroke:"#1D9FF4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M17 9.65039L13.87 12.1504C12.84 12.9704 11.15 12.9704 10.12 12.1504L7 9.65039",stroke:"#1D9FF4",strokeWidth:"1.5",strokeMiterlimit:"10",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("input",{type:"email",placeholder:"Enter Email",className:"w-full rounded-2xl border-0 py-3 pl-10 pr-24 text-sm focus:outline-none focus:ring-0 sm:py-4 sm:pl-12 sm:pr-32 sm:text-base"}),e.jsxs("button",{className:"absolute right-1.5 top-1/2 flex -translate-y-1/2 items-center justify-center gap-1 whitespace-nowrap rounded-2xl bg-[#004540] px-3 py-2 text-xs text-white sm:gap-2 sm:px-6 sm:py-2.5 sm:text-base",children:["Book your slot"," ",e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 18 19",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-5 sm:w-5",children:[e.jsx("path",{d:"M4.67969 14.1504L13.3197 5.15039",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6.30078 5.15039H13.3208V12.4629",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]})]})]})]})}),e.jsx("div",{className:"pointer-events-none absolute bottom-0 left-0 z-40 w-full",children:e.jsx("svg",{viewBox:"0 0 1440 320",className:"h-[120px] w-full md:h-[180px]",preserveAspectRatio:"none",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fill:"#F9F9F6",d:"M0,160 C480,320 960,0 1440,160 L1440,320 L0,320 Z"})})})]}),e.jsx("section",{className:"bg-gray-50 px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto flex max-w-7xl flex-col items-center gap-8 md:flex-row md:gap-12",children:[e.jsxs("div",{className:"w-full md:w-1/2","data-aos":"fade-right","data-aos-duration":"1000",children:[e.jsx("h2",{className:"font-ubuntu mb-4 text-2xl font-bold sm:mb-6 sm:text-3xl",children:"Welcome"}),e.jsx("p",{className:"font-inter mb-3 text-sm text-gray-600 sm:mb-4 sm:text-base",children:"Court Matchup is the easiest way for racquet clubs to manage bookings, lessons, and player matching, all in one simple platform."}),e.jsx(x.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"font-ubuntu w-full rounded-xl bg-[#005954] px-4 py-2.5 text-sm font-bold text-[#B8FE22] transition-colors hover:bg-[#004540] sm:w-auto sm:px-5 sm:py-3 sm:text-base",children:"Get Started"})]}),e.jsx("div",{className:"relative mt-6 w-full md:mt-0 md:w-1/2","data-aos":"fade-left","data-aos-duration":"1000","data-aos-delay":"200",children:e.jsx("div",{className:"relative overflow-hidden rounded-lg",children:e.jsx("img",{src:"/images/landing/welcome-image.png",alt:"Tennis Court",className:"h-full w-full object-cover",loading:"lazy"})})})]})}),e.jsx("section",{id:"benefits",className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Benefits"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Why Choose Court Matchup"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Our platform offers unique advantages for players, coaches, and clubs alike. Discover how Court Matchup can enhance your tennis experience."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:[e.jsxs("div",{className:"overflow-hidden rounded-xl bg-[#F9F9F6] shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"100",children:[e.jsxs("div",{className:"relative h-40 overflow-hidden",children:[e.jsx("img",{src:"/images/landing/tennis-player.jpg",alt:"Easy Booking",className:"h-full w-full object-cover",loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),e.jsx("div",{className:"absolute bottom-0 left-0 flex h-16 w-16 items-center justify-center rounded-tr-xl bg-[#005954]",children:e.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.75 4.75V2.75M16.25 4.75V2.75M4.75 20.25H19.25C19.8023 20.25 20.25 19.8023 20.25 19.25V5.75C20.25 5.19772 19.8023 4.75 19.25 4.75H4.75C4.19772 4.75 3.75 5.19772 3.75 5.75V19.25C3.75 19.8023 4.19772 20.25 4.75 20.25Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 9.75V15.25M14.75 12.5H9.25",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}),e.jsxs("div",{className:"p-5",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Easy Booking"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Book courts, lessons, and clinics with just a few clicks. Our intuitive interface makes scheduling simple and hassle-free."})]})]}),e.jsxs("div",{className:"overflow-hidden rounded-xl bg-[#F9F9F6] shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"200",children:[e.jsxs("div",{className:"relative h-40 overflow-hidden",children:[e.jsx("img",{src:"/images/landing/male-tennis-player.jpg",alt:"Find Partners",className:"h-full w-full object-cover",loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),e.jsx("div",{className:"absolute bottom-0 left-0 flex h-16 w-16 items-center justify-center rounded-tr-xl bg-[#005954]",children:e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M19.5005 8.75V11.5M19.5005 11.5V14.25M19.5005 11.5H16.7505M19.5005 11.5H22.2505M14.7505 6.5C14.7505 8.57107 13.0716 10.25 11.0005 10.25C8.92942 10.25 7.25049 8.57107 7.25049 6.5C7.25049 4.42893 8.92942 2.75 11.0005 2.75C13.0716 2.75 14.7505 4.42893 14.7505 6.5ZM3.67204 19.1657C4.4344 15.7735 7.20488 13.25 11.0005 13.25C14.7961 13.25 17.5666 15.7735 18.3289 19.1657C18.4581 19.7406 17.9913 20.25 17.4021 20.25H4.59891C4.00965 20.25 3.54284 19.7406 3.67204 19.1657Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"p-5",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Find Partners"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:'Connect with players of similar skill levels. Our "Find a Buddy" feature helps you discover the perfect match for your game.'})]})]}),e.jsxs("div",{className:"overflow-hidden rounded-xl bg-[#F9F9F6] shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"300",children:[e.jsxs("div",{className:"relative h-40 overflow-hidden",children:[e.jsx("img",{src:"/images/landing/female-tennis-player.jpg",alt:"Expert Coaching",className:"h-full w-full object-cover",loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),e.jsx("div",{className:"absolute bottom-0 left-0 flex h-16 w-16 items-center justify-center rounded-tr-xl bg-[#005954]",children:e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.75 8.75L19.25 12L15.75 15.25M8.25 15.25L4.75 12L8.25 8.75M13 6L11 18",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"p-5",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Expert Coaching"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Access top-tier coaching and clinics. Improve your skills with personalized lessons from experienced professionals."})]})]}),e.jsxs("div",{className:"overflow-hidden rounded-xl bg-[#F9F9F6] shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"400",children:[e.jsxs("div",{className:"relative h-40 overflow-hidden",children:[e.jsx("img",{src:"/images/landing/team-tennis.jpg",alt:"Community Events",className:"h-full w-full object-cover",loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"}),e.jsx("div",{className:"absolute bottom-0 left-0 flex h-16 w-16 items-center justify-center rounded-tr-xl bg-[#005954]",children:e.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M18 7.16C17.94 7.15 17.87 7.15 17.81 7.16C16.43 7.11 15.33 5.98 15.33 4.58C15.33 3.15 16.48 2 17.91 2C19.34 2 20.49 3.16 20.49 4.58C20.48 5.98 19.38 7.11 18 7.16Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M16.9699 14.44C18.3399 14.67 19.8499 14.43 20.9099 13.72C22.3199 12.78 22.3199 11.24 20.9099 10.3C19.8399 9.59004 18.3099 9.35003 16.9399 9.59003",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M5.96998 7.16C6.02998 7.15 6.09998 7.15 6.15998 7.16C7.53998 7.11 8.63998 5.98 8.63998 4.58C8.63998 3.15 7.48998 2 6.05998 2C4.62998 2 3.47998 3.16 3.47998 4.58C3.48998 5.98 4.58998 7.11 5.96998 7.16Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7 14.44C5.63 14.67 4.12 14.43 3.06 13.72C1.65 12.78 1.65 11.24 3.06 10.3C4.13 9.59004 5.66 9.35003 7.03 9.59003",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M12 14.63C11.94 14.62 11.87 14.62 11.81 14.63C10.43 14.58 9.32996 13.45 9.32996 12.05C9.32996 10.62 10.48 9.47 11.91 9.47C13.34 9.47 14.49 10.63 14.49 12.05C14.48 13.45 13.38 14.59 12 14.63Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M9.08997 17.78C7.67997 18.72 7.67997 20.26 9.08997 21.2C10.69 22.27 13.31 22.27 14.91 21.2C16.32 20.26 16.32 18.72 14.91 17.78C13.32 16.72 10.69 16.72 9.08997 17.78Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}),e.jsxs("div",{className:"p-5",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Community Events"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Join tournaments, social events, and clinics. Build connections and enjoy the vibrant tennis community on our platform."})]})]})]}),e.jsxs("div",{className:"mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:[e.jsxs("div",{className:"flex items-center gap-4 rounded-xl bg-[#F9F9F6] p-4 shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"100",children:[e.jsx("div",{className:"flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-[#005954]",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 14.5V16.5M7 10.5L12 14.5L17 10.5M7.8 21H16.2C17.8802 21 18.7202 21 19.362 20.673C19.9265 20.3854 20.3854 19.9265 20.673 19.362C21 18.7202 21 17.8802 21 16.2V7.8C21 6.11984 21 5.27976 20.673 4.63803C20.3854 4.07354 19.9265 3.6146 19.362 3.32698C18.7202 3 17.8802 3 16.2 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-ubuntu mb-1 text-lg font-bold text-[#004540]",children:"Mobile App Access"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Manage your tennis activities on the go with our convenient mobile app."})]})]}),e.jsxs("div",{className:"flex items-center gap-4 rounded-xl bg-[#F9F9F6] p-4 shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"200",children:[e.jsx("div",{className:"flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-[#005954]",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9 12L11 14L15 10M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-ubuntu mb-1 text-lg font-bold text-[#004540]",children:"Secure Payments"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Enjoy safe and transparent transactions for all your bookings and services."})]})]}),e.jsxs("div",{className:"flex items-center gap-4 rounded-xl bg-[#F9F9F6] p-4 shadow-md transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"300",children:[e.jsx("div",{className:"flex h-14 w-14 flex-shrink-0 items-center justify-center rounded-full bg-[#005954]",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 10V20M8 10L4 9.99998V20L8 20M8 10L13.1956 3.93847C13.6886 3.3633 14.4642 3.11604 15.1992 3.29977L15.2467 3.31166C16.5885 3.64711 17.1929 5.21057 16.4258 6.36135L14 9.99998H18.5604C19.8225 9.99998 20.7691 11.1546 20.5216 12.3922L19.3216 18.3922C19.1346 19.3271 18.3138 20 17.3604 20L8 20",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-ubuntu mb-1 text-lg font-bold text-[#004540]",children:"Personalized Experience"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Receive tailored recommendations based on your preferences and playing history."})]})]})]})]})}),e.jsx("section",{id:"features",className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-6 text-center sm:mb-10 md:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})," ",e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Features"})]}),e.jsx("h2",{className:"font-ubuntu text-xl font-bold text-[#005954] sm:text-2xl md:text-3xl",children:"Why people choose our company"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-x-4 gap-y-12 sm:gap-x-6 sm:gap-y-16 md:grid-cols-2 lg:grid-cols-3",children:de.map((t,l)=>e.jsx("div",{"data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":100*l,children:e.jsx(oe,{icon:t.icon,iconSize:t.iconSize,title:t.title,description:t.description})},t.id))})]})}),e.jsx("section",{id:"coaches",className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Our Coaches"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Meet Our Expert Coaches"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Our certified professionals are dedicated to helping you improve your game and reach your full potential."})]}),e.jsx("div",{className:"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4",children:z.map(t=>e.jsxs("div",{className:"overflow-hidden rounded-xl bg-[#F9F9F6] shadow-md transition-all duration-300 hover:-translate-y-2 hover:shadow-xl","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":t.id*100,children:[e.jsxs("div",{className:"relative h-64 overflow-hidden",children:[e.jsx("img",{src:t.image,alt:t.name,className:"h-full w-full object-cover",loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"})]}),e.jsxs("div",{className:"p-5",children:[e.jsx("h3",{className:"font-ubuntu mb-1 text-xl font-bold text-[#004540]",children:t.name}),e.jsxs("div",{className:"mb-3 flex items-center text-sm text-gray-600",children:[e.jsx(S,{className:"mr-1 text-[#005954]"}),e.jsx("span",{children:t.club})]}),e.jsx("p",{className:"font-inter mb-4 text-sm text-gray-600",children:t.bio}),e.jsx("div",{className:"flex flex-wrap gap-2",children:t.specialties.map((l,h)=>e.jsx("span",{className:"rounded-full bg-[#e8f5f4] px-2 py-1 text-xs font-medium text-[#005954]",children:l},h))})]})]},t.id))})]})}),e.jsx("section",{id:"pricing",className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Pricing"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Simple, Transparent Pricing"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Choose the plan that works best for your club. All plans include core features with options to scale as you grow."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"flex flex-col overflow-hidden rounded-2xl bg-white shadow-lg transition-transform duration-300 hover:-translate-y-2 hover:shadow-xl","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"100",children:[e.jsxs("div",{className:"bg-[#F0F0F0] p-6",children:[e.jsx("h3",{className:"font-ubuntu text-xl font-bold text-[#004540]",children:"Basic"}),e.jsxs("div",{className:"mt-4 flex items-baseline",children:[e.jsx("span",{className:"font-ubuntu text-4xl font-bold text-[#005954]",children:"$99"}),e.jsx("span",{className:"ml-1 text-gray-500",children:"/month"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Perfect for small clubs just getting started"})]}),e.jsxs("div",{className:"flex flex-1 flex-col justify-between p-6",children:[e.jsxs("ul",{className:"mb-6 space-y-4",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Up to 5 courts"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Basic court booking"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Email support"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Basic reporting"})]})]}),e.jsx("button",{className:"w-full rounded-xl bg-[#005954] py-3 font-bold text-white transition-colors hover:bg-[#004540]",children:"Get Started"})]})]}),e.jsxs("div",{className:"flex flex-col overflow-hidden rounded-2xl bg-white shadow-lg transition-transform duration-300 hover:-translate-y-2 hover:shadow-xl","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"200",children:[e.jsxs("div",{className:"relative bg-[#005954] p-6 text-white",children:[e.jsx("div",{className:"absolute right-4 top-4 rounded-full bg-[#B8FE22] px-3 py-1 text-xs font-bold text-[#004540]",children:"Popular"}),e.jsx("h3",{className:"font-ubuntu text-xl font-bold",children:"Pro"}),e.jsxs("div",{className:"mt-4 flex items-baseline",children:[e.jsx("span",{className:"font-ubuntu text-4xl font-bold",children:"$199"}),e.jsx("span",{className:"ml-1 opacity-80",children:"/month"})]}),e.jsx("p",{className:"mt-2 text-sm opacity-90",children:"Ideal for growing clubs with multiple services"})]}),e.jsxs("div",{className:"flex flex-1 flex-col justify-between p-6",children:[e.jsxs("ul",{className:"mb-6 space-y-4",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Up to 15 courts"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Advanced booking features"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Coach management"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Priority support"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Advanced analytics"})]})]}),e.jsx("button",{className:"w-full rounded-xl bg-[#005954] py-3 font-bold text-white transition-colors hover:bg-[#004540]",children:"Get Started"})]})]}),e.jsxs("div",{className:"flex flex-col overflow-hidden rounded-2xl bg-white shadow-lg transition-transform duration-300 hover:-translate-y-2 hover:shadow-xl","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"300",children:[e.jsxs("div",{className:"bg-[#F0F0F0] p-6",children:[e.jsx("h3",{className:"font-ubuntu text-xl font-bold text-[#004540]",children:"Enterprise"}),e.jsxs("div",{className:"mt-4 flex items-baseline",children:[e.jsx("span",{className:"font-ubuntu text-4xl font-bold text-[#005954]",children:"$399"}),e.jsx("span",{className:"ml-1 text-gray-500",children:"/month"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"For large clubs with complex requirements"})]}),e.jsxs("div",{className:"flex flex-1 flex-col justify-between p-6",children:[e.jsxs("ul",{className:"mb-6 space-y-4",children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Unlimited courts"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Custom branding"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"API access"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Dedicated account manager"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-2 h-5 w-5 flex-shrink-0 text-[#005954]",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("span",{className:"text-sm",children:"Custom integrations"})]})]}),e.jsx("button",{className:"w-full rounded-xl bg-[#005954] py-3 font-bold text-white transition-colors hover:bg-[#004540]",children:"Contact Sales"})]})]})]})]})}),e.jsx("div",{className:"pointer-events-none relative w-full bg-white",children:e.jsx("svg",{viewBox:"0 0 1440 320",className:"h-[120px] w-full md:h-[180px]",preserveAspectRatio:"none",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fill:"#F9FAFB",d:"M0,160 C480,320 960,0 1440,160 L1440,320 L0,320 Z"})})}),e.jsx("section",{className:"relative bg-[#F9FAFB] px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-6 text-center sm:mb-8","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-4 py-2 text-xs font-medium uppercase tracking-wider text-gray-500 !shadow-3 shadow-black sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"17",height:"17",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})," ",e.jsx("span",{className:"text-xs font-bold text-[#005954] ",children:"Services"})]}),e.jsx("h2",{className:"font-ubuntu mt-1 text-2xl font-bold text-gray-800 sm:mt-2 sm:text-3xl",children:"A brief description of the key services"})]}),e.jsxs("div",{className:"mb-8 grid grid-cols-1 gap-4 sm:mb-12 sm:gap-6 md:grid-cols-2 lg:grid-cols-3",children:[e.jsxs("div",{className:"relative overflow-hidden rounded-2xl shadow-md sm:rounded-3xl","data-aos":"zoom-in","data-aos-duration":"800",children:[e.jsx("img",{src:"/images/landing/female-tennis-player.jpg",alt:"Female Tennis Player",className:"h-40 w-full object-cover sm:h-48",loading:"lazy"}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white sm:p-4",children:[e.jsx("p",{className:"text-sm font-bold sm:text-base",children:"Over 5,000 Hours of Coaching Experience."}),e.jsx("p",{className:"text-xs sm:text-sm",children:"Trainer"})]})]}),e.jsxs("div",{className:"relative overflow-hidden rounded-2xl shadow-md sm:rounded-3xl","data-aos":"zoom-in","data-aos-duration":"800","data-aos-delay":"100",children:[e.jsx("img",{src:"/images/landing/male-tennis-player.jpg",alt:"Male Tennis Player",className:"h-40 w-full object-cover sm:h-48",loading:"lazy"}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white sm:p-4",children:[e.jsx("p",{className:"text-sm font-bold sm:text-base",children:"Over 5,000 Hours of Coaching Experience."}),e.jsx("p",{className:"text-xs sm:text-sm",children:"Trainer"})]})]}),e.jsxs("div",{className:"relative overflow-hidden rounded-2xl shadow-md sm:rounded-3xl","data-aos":"zoom-in","data-aos-duration":"800","data-aos-delay":"200",children:[e.jsx("img",{src:"/images/landing/tennis-player.jpg",alt:"Tennis Player",className:"h-40 w-full object-cover sm:h-48",loading:"lazy"}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white sm:p-4",children:[e.jsx("p",{className:"text-sm font-bold sm:text-base",children:"Over 5,000 Hours of Coaching Experience."}),e.jsx("p",{className:"text-xs sm:text-sm",children:"Trainer"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"relative overflow-hidden rounded-2xl shadow-md sm:rounded-3xl","data-aos":"fade-right","data-aos-duration":"800",children:[e.jsx("img",{src:"/images/landing/male-tennis-player2.jpg",alt:"Male Tennis Player",className:"h-40 w-full object-cover sm:h-48",loading:"lazy"}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white sm:p-4",children:[e.jsx("p",{className:"text-sm font-bold sm:text-base",children:"Over 5,000 Hours of Coaching Experience."}),e.jsx("p",{className:"text-xs sm:text-sm",children:"Trainer"})]})]}),e.jsxs("div",{className:"my-6 text-center md:mb-0","data-aos":"fade-up","data-aos-duration":"1000","data-aos-delay":"200",children:[e.jsx("h3",{className:"font-ubuntu mb-1 text-2xl font-bold sm:mb-2 sm:text-4xl",children:"Have 50+"}),e.jsx("p",{className:"text-base font-bold sm:text-xl",children:"Professional trainer and types of exercises"})]}),e.jsxs("div",{className:"relative overflow-hidden rounded-2xl shadow-md sm:rounded-3xl","data-aos":"fade-left","data-aos-duration":"800",children:[e.jsx("img",{src:"/images/landing/male-tennis-player3.jpg",alt:"Male Tennis Player",className:"h-40 w-full object-cover sm:h-48",loading:"lazy"}),e.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3 text-white sm:p-4",children:[e.jsx("p",{className:"text-sm font-bold sm:text-base",children:"Over 5,000 Hours of Coaching Experience."}),e.jsx("p",{className:"text-xs sm:text-sm",children:"Trainer"})]})]})]})]})}),e.jsx("div",{className:"pointer-events-none relative w-full bg-gray-50",children:e.jsx("svg",{viewBox:"0 0 1440 320",className:"h-[120px] w-full md:h-[180px]",preserveAspectRatio:"none",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fill:"#FFFFFF",d:"M0,160 C480,320 960,0 1440,160 L1440,320 L0,320 Z"})})}),e.jsx("section",{className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Process"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"How It Works"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Court Matchup makes it easy to book courts, find playing partners, and join clinics. Here's how our platform works in a few simple steps."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4",children:[e.jsxs("div",{className:"flex flex-col items-center","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"100",children:[e.jsx("div",{className:"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-[#005954] text-white",children:e.jsx("span",{className:"text-3xl font-bold",children:"1"})}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -left-4 top-0 h-full w-1 bg-[#005954] md:hidden"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Sign Up"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Create your account and set up your profile with your playing preferences and skill level."})]})]}),e.jsx("div",{className:"mt-4 h-40 w-full overflow-hidden rounded-lg",children:e.jsx("img",{src:"/images/landing/male-tennis-player.jpg",alt:"Sign Up",className:"h-full w-full object-cover",loading:"lazy"})})]}),e.jsxs("div",{className:"flex flex-col items-center","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"200",children:[e.jsx("div",{className:"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-[#005954] text-white",children:e.jsx("span",{className:"text-3xl font-bold",children:"2"})}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -left-4 top-0 h-full w-1 bg-[#005954] md:hidden"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Find a Court"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Browse available courts by location, sport type, and availability to find the perfect match."})]})]}),e.jsx("div",{className:"mt-4 h-40 w-full overflow-hidden rounded-lg",children:e.jsx("img",{src:"/images/landing/tennis-player.jpg",alt:"Find a Court",className:"h-full w-full object-cover",loading:"lazy"})})]}),e.jsxs("div",{className:"flex flex-col items-center","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"300",children:[e.jsx("div",{className:"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-[#005954] text-white",children:e.jsx("span",{className:"text-3xl font-bold",children:"3"})}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -left-4 top-0 h-full w-1 bg-[#005954] md:hidden"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Book & Play"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Reserve your court time, invite friends or find partners, and enjoy your game with ease."})]})]}),e.jsx("div",{className:"mt-4 h-40 w-full overflow-hidden rounded-lg",children:e.jsx("img",{src:"/images/landing/female-tennis-player.jpg",alt:"Book & Play",className:"h-full w-full object-cover",loading:"lazy"})})]}),e.jsxs("div",{className:"flex flex-col items-center","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"400",children:[e.jsx("div",{className:"mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-[#005954] text-white",children:e.jsx("span",{className:"text-3xl font-bold",children:"4"})}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-xl font-bold text-[#004540]",children:"Join Activities"}),e.jsx("p",{className:"font-inter text-sm text-gray-600",children:"Participate in clinics, tournaments, and social events to improve your skills and meet other players."})]}),e.jsx("div",{className:"mt-4 h-40 w-full overflow-hidden rounded-lg",children:e.jsx("img",{src:"/images/landing/team-tennis.jpg",alt:"Join Activities",className:"h-full w-full object-cover",loading:"lazy"})})]})]}),e.jsxs("div",{className:"mt-12 flex flex-col items-center justify-center sm:mt-16","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"500",children:[e.jsx(x.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"font-ubuntu w-full rounded-xl bg-[#005954] px-6 py-3 text-base font-bold text-[#B8FE22] transition-colors hover:bg-[#004540] sm:w-auto sm:px-8 sm:py-4 sm:text-lg",children:"Get Started Today"}),e.jsx("p",{className:"mt-4 text-center text-sm text-gray-500",children:"Join thousands of players already using Court Matchup"})]})]})}),e.jsx("section",{className:"mt-10 bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"relative rounded-2xl bg-[#005954] p-4 text-white sm:rounded-3xl sm:p-6","data-aos":"flip-left","data-aos-duration":"1000",children:[e.jsxs("div",{className:"relative z-10",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-lg font-bold sm:mb-4 sm:text-xl",children:"Elevate Your Game To New Heights."}),e.jsx("p",{className:"font-inter text-xs sm:text-sm",children:"It is a long established fact that a reader will be distracted by the readable content."})]}),e.jsx("img",{src:"/images/landing/tennis-ball-for-bg.png",alt:"Tennis Racket",className:"absolute right-0 top-0 z-0 h-full opacity-70 sm:opacity-100",loading:"lazy"})]}),e.jsx("div",{className:"h-48 overflow-hidden rounded-2xl sm:h-56 sm:rounded-3xl","data-aos":"zoom-in","data-aos-duration":"1000","data-aos-delay":"100",children:e.jsx("img",{src:"/images/landing/female-tennis-player.jpg",alt:"Female Tennis Player",className:"h-full w-full object-cover",loading:"lazy"})}),e.jsxs("div",{className:"relative rounded-2xl bg-[#B8A369] p-4 text-white sm:rounded-3xl sm:p-6","data-aos":"flip-right","data-aos-duration":"1000","data-aos-delay":"200",children:[e.jsxs("div",{className:"relative z-10",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-lg font-bold sm:mb-4 sm:text-xl",children:"From Beginners To Pros"}),e.jsx("p",{className:"font-inter text-xs sm:text-sm",children:"Built for clubs of all sizes — from recreational players to competitive athletes.”"})]}),e.jsx("img",{src:"/images/landing/tennis-ball-for-bg.png",alt:"Tennis Racket",className:"absolute right-0 top-0 z-0 h-full opacity-70 sm:opacity-100",loading:"lazy"})]})]}),e.jsxs("div",{className:"mt-4 grid grid-cols-1 gap-4 sm:mt-6 sm:gap-6 md:grid-cols-12",children:[e.jsx("div",{className:"h-48 overflow-hidden rounded-2xl sm:h-56 sm:rounded-3xl md:col-span-4","data-aos":"fade-right","data-aos-duration":"1000",children:e.jsx("img",{src:"/images/landing/male-tennis-player.jpg",alt:"Tennis Player",className:"h-full w-full object-cover",loading:"lazy"})}),e.jsxs("div",{className:"relative overflow-hidden rounded-2xl bg-[#005954] p-4 text-white sm:rounded-3xl sm:p-6 md:col-span-5","data-aos":"fade-up","data-aos-duration":"1000","data-aos-delay":"100",children:[e.jsxs("div",{className:"relative z-10",children:[e.jsx("h3",{className:"font-ubuntu mb-2 text-lg font-bold sm:mb-4 sm:text-xl",children:"We Welcome You At Our Club"}),e.jsx("p",{className:"font-inter text-xs sm:text-sm",children:"It is a long established fact that a reader will be distracted by the readable content."})]}),e.jsx("img",{src:"/images/landing/tennis-racket-for-bg.png",alt:"Tennis Racket",className:"absolute right-0 top-0 z-0 h-full opacity-70 sm:opacity-100",loading:"lazy"})]}),e.jsx("div",{className:"h-48 overflow-hidden rounded-2xl sm:h-56 sm:rounded-3xl md:col-span-3","data-aos":"fade-left","data-aos-duration":"1000","data-aos-delay":"200",children:e.jsx("img",{src:"/images/landing/racket-with-tennis-ball.jpg",alt:"Tennis Ball and Racket",className:"h-full w-full object-cover",loading:"lazy"})})]})]})}),e.jsxs("section",{className:"relative px-4 pb-32 pt-12 sm:px-6 sm:pb-40 sm:pt-16",children:[e.jsx("div",{className:"absolute inset-0 z-10","data-aos":"fade-in","data-aos-duration":"1500",children:e.jsx("img",{src:"/images/landing/team-tennis.jpg",alt:"Team Tennis",className:"h-full w-full object-cover",loading:"lazy"})}),e.jsx("div",{className:"absolute inset-0 z-20 bg-[#005359] opacity-85"}),e.jsxs("div",{className:"container relative z-30 mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-down","data-aos-duration":"800",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-center gap-2 sm:mb-5 sm:gap-5",children:[e.jsx("svg",{width:"40",height:"6",viewBox:"0 0 68 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"hidden sm:block sm:w-[68px]",children:e.jsx("path",{d:"M67.6667 3.01172C67.6667 1.53896 66.4728 0.345052 65 0.345052C63.5272 0.345052 62.3333 1.53896 62.3333 3.01172C62.3333 4.48448 63.5272 5.67839 65 5.67839C66.4728 5.67839 67.6667 4.48448 67.6667 3.01172ZM65 2.51172L0.0124512 2.51171L0.0124511 3.51171L65 3.51172L65 2.51172Z",fill:"#C5EA48"})}),e.jsx("div",{className:"inline-block rounded-full bg-white px-3 py-0.5 sm:px-4 sm:py-1",children:e.jsx("span",{className:"text-sm font-medium text-[#005954] sm:text-base",children:"Reviews"})}),e.jsx("svg",{width:"40",height:"6",viewBox:"0 0 68 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"hidden sm:block sm:w-[68px]",children:e.jsx("path",{d:"M0.333333 3.01172C0.333333 1.53896 1.52724 0.345052 3 0.345052C4.47276 0.345052 5.66667 1.53896 5.66667 3.01172C5.66667 4.48448 4.47276 5.67839 3 5.67839C1.52724 5.67839 0.333333 4.48448 0.333333 3.01172ZM3 2.51172L67.9875 2.51171L67.9875 3.51171L3 3.51172L3 2.51172Z",fill:"#C5EA48"})})]}),e.jsx(x.h2,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.3},className:"font-ubuntu text-2xl font-bold text-white sm:text-3xl",children:"Success Stories"})]}),e.jsx("div",{className:"absolute left-0 right-0 mx-auto w-full overflow-visible",children:e.jsx("div",{className:"relative z-10 mx-auto flex w-full max-w-7xl items-stretch justify-center px-4 pb-20 pt-10 sm:px-6 sm:pb-24","data-aos":"fade-up","data-aos-duration":"1000","data-aos-offset":"200",children:e.jsx(fe,{})})})]})]}),e.jsx("section",{className:"mt-[8rem] bg-white px-4 py-12 sm:mt-[10rem] sm:px-6 sm:py-16 md:mt-[12rem]",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-6 text-center sm:mb-8","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Find Clubs"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Discover Court Matchup Clubs"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Find Court Matchup clubs near you. Search and explore clubs on the map, click on pins to see details, and visit their pages to learn more."})]}),e.jsx("div",{className:"mt-6 sm:mt-8","data-aos":"zoom-in","data-aos-duration":"1000","data-aos-delay":"200",children:e.jsx(pe,{clubs:w})})]})}),e.jsx(ne,{}),e.jsx("section",{id:"contact",className:"bg-white px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-8 text-center sm:mb-12","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Contact Us"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Get in Touch"}),e.jsx("p",{className:"mx-auto mt-2 max-w-2xl text-xs text-gray-600 sm:mt-4 sm:text-sm md:text-base",children:"Have questions about our platform? Ready to transform your club's operations? We're here to help."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-xl bg-[#F9F9F6] p-6 shadow-md sm:p-8","data-aos":"fade-right","data-aos-duration":"800",children:[e.jsx("h3",{className:"font-ubuntu mb-4 text-xl font-bold text-[#004540] sm:text-2xl",children:"Book a Demo"}),e.jsxs("form",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:"First Name"}),e.jsx("input",{type:"text",id:"firstName",className:"w-full rounded-lg border border-gray-300 p-2.5 text-sm focus:border-[#005954] focus:outline-none focus:ring-1 focus:ring-[#005954]",placeholder:"John",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"lastName",className:"mb-1 block text-sm font-medium text-gray-700",children:"Last Name"}),e.jsx("input",{type:"text",id:"lastName",className:"w-full rounded-lg border border-gray-300 p-2.5 text-sm focus:border-[#005954] focus:outline-none focus:ring-1 focus:ring-[#005954]",placeholder:"Doe",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"mb-1 block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",id:"email",className:"w-full rounded-lg border border-gray-300 p-2.5 text-sm focus:border-[#005954] focus:outline-none focus:ring-1 focus:ring-[#005954]",placeholder:"<EMAIL>",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"clubName",className:"mb-1 block text-sm font-medium text-gray-700",children:"Club Name"}),e.jsx("input",{type:"text",id:"clubName",className:"w-full rounded-lg border border-gray-300 p-2.5 text-sm focus:border-[#005954] focus:outline-none focus:ring-1 focus:ring-[#005954]",placeholder:"Your Tennis Club"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"message",className:"mb-1 block text-sm font-medium text-gray-700",children:"Message"}),e.jsx("textarea",{id:"message",rows:"4",className:"w-full rounded-lg border border-gray-300 p-2.5 text-sm focus:border-[#005954] focus:outline-none focus:ring-1 focus:ring-[#005954]",placeholder:"Tell us about your club and what you're looking for..."})]}),e.jsx("button",{type:"submit",className:"w-full rounded-lg bg-[#005954] px-5 py-3 text-center font-medium text-white hover:bg-[#004540] focus:outline-none focus:ring-2 focus:ring-[#005954] focus:ring-offset-2",children:"Request Demo"})]})]}),e.jsx("div",{className:"flex flex-col justify-between","data-aos":"fade-left","data-aos-duration":"800","data-aos-delay":"100",children:e.jsxs("div",{className:"mb-8 rounded-xl bg-[#005954] p-6 text-white shadow-md sm:p-8",children:[e.jsx("h3",{className:"font-ubuntu mb-4 text-xl font-bold sm:text-2xl",children:"Contact Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-3 h-6 w-6 flex-shrink-0 text-[#B8FE22]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Phone"}),e.jsx("p",{className:"mt-1",children:"(*************"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("svg",{className:"mr-3 h-6 w-6 flex-shrink-0 text-[#B8FE22]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Email"}),e.jsx("p",{className:"mt-1",children:"<EMAIL>"})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsxs("svg",{className:"mr-3 h-6 w-6 flex-shrink-0 text-[#B8FE22]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:"Address"}),e.jsx("p",{className:"mt-1",children:"123 Tennis Court Lane, Suite 100"}),e.jsx("p",{children:"San Francisco, CA 94107"})]})]})]})]})})]})]})}),e.jsx("section",{className:"bg-gray-50 px-4 py-12 sm:px-6 sm:py-16",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-6 text-center sm:mb-8","data-aos":"fade-up","data-aos-duration":"800",children:[e.jsxs("div",{className:"mx-auto mb-2 flex max-w-fit items-center justify-center gap-1 rounded-full border border-black/10 px-3 py-1.5 text-xs font-medium uppercase tracking-wider text-gray-500 shadow-sm sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{children:e.jsxs("svg",{width:"14",height:"14",viewBox:"0 0 17 17",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-[17px] sm:w-[17px]",children:[e.jsx("path",{d:"M13.9016 9.70156H9.70156V13.9016H13.9016V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 9.70156H3.10156V13.9016H7.30156V9.70156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M13.9016 3.10156H9.70156V7.30156H13.9016V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.30156 3.10156H3.10156V7.30156H7.30156V3.10156Z",stroke:"#415C41",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsx("span",{className:"text-xs font-bold text-[#005954] sm:text-sm",children:"Gallery"})]}),e.jsx("h2",{className:"font-ubuntu text-2xl font-bold text-[#004540] sm:text-3xl md:text-4xl",children:"Our gallery"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 md:gap-8",children:[e.jsx("div",{className:"flex h-full w-full items-stretch","data-aos":"fade-right","data-aos-duration":"1000",children:e.jsxs("div",{className:"relative aspect-[4/3] w-full overflow-hidden rounded-2xl sm:rounded-3xl md:aspect-[5/6]",children:[e.jsx("img",{src:"/images/landing/male-tennis-player.jpg",alt:"Tennis players",className:"h-full w-full rounded-2xl object-cover transition-transform duration-300 hover:scale-105 sm:rounded-3xl",loading:"lazy"}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 rounded-b-2xl bg-black/60 p-3 text-white sm:rounded-b-3xl",children:e.jsx("p",{className:"text-sm font-bold sm:text-base",children:"Junior Tournament"})})]})}),e.jsxs("div",{className:"mt-4 flex h-full flex-col justify-between gap-4 md:mt-0","data-aos":"fade-left","data-aos-duration":"1000","data-aos-delay":"200",children:[e.jsxs("div",{className:"flex h-1/2 gap-4",children:[e.jsxs("div",{className:"relative aspect-square flex-1 overflow-hidden rounded-2xl sm:rounded-3xl",children:[e.jsx("img",{src:"/images/landing/racket-with-tennis-ball.jpg",alt:"Tennis ball and racket",className:"h-full w-full rounded-2xl object-cover transition-transform duration-300 hover:scale-105 sm:rounded-3xl",loading:"lazy"}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 rounded-b-2xl bg-black/60 p-2 text-white sm:rounded-b-3xl",children:e.jsx("p",{className:"text-xs font-bold sm:text-sm",children:"Equipment Essentials"})})]}),e.jsxs("div",{className:"relative aspect-square flex-1 overflow-hidden rounded-2xl sm:rounded-3xl",children:[e.jsx("img",{src:"/images/landing/male-tennis-player3.jpg",alt:"Tennis player",className:"h-full w-full rounded-2xl object-cover transition-transform duration-300 hover:scale-105 sm:rounded-3xl",loading:"lazy"}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 rounded-b-2xl bg-black/60 p-2 text-white sm:rounded-b-3xl",children:e.jsx("p",{className:"text-xs font-bold sm:text-sm",children:"Advanced Training"})})]})]}),e.jsxs("div",{className:"flex h-1/2 gap-4",children:[e.jsxs("div",{className:"relative aspect-square flex-1 overflow-hidden rounded-2xl sm:rounded-3xl",children:[e.jsx("img",{src:"/images/landing/female-tennis-player.jpg",alt:"Female tennis player",className:"h-full w-full rounded-2xl object-cover transition-transform duration-300 hover:scale-105 sm:rounded-3xl",loading:"lazy"}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 rounded-b-2xl bg-black/60 p-2 text-white sm:rounded-b-3xl",children:e.jsx("p",{className:"text-xs font-bold sm:text-sm",children:"Women's Doubles Clinic"})})]}),e.jsxs("div",{className:"relative aspect-square flex-1 overflow-hidden rounded-2xl sm:rounded-3xl",children:[e.jsx("img",{src:"/images/landing/male-tennis-player2.jpg",alt:"Tennis player",className:"h-full w-full rounded-2xl object-cover transition-transform duration-300 hover:scale-105 sm:rounded-3xl",loading:"lazy"}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 rounded-b-2xl bg-black/60 p-2 text-white sm:rounded-b-3xl",children:e.jsx("p",{className:"text-xs font-bold sm:text-sm",children:"Professional Coaching"})})]})]})]})]}),e.jsxs("div",{className:"mt-6 flex justify-between","data-aos":"fade-up","data-aos-duration":"800","data-aos-delay":"300",children:[e.jsx(x.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"flex h-8 w-8 items-center justify-center rounded-xl bg-[#004540] text-white transition-colors hover:bg-[#003530] sm:h-10 sm:w-10 md:h-12 md:w-12",children:e.jsx(E,{className:"h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5"})}),e.jsx(x.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"flex h-8 w-8 items-center justify-center rounded-xl bg-[#004540] text-white transition-colors hover:bg-[#003530] sm:h-10 sm:w-10 md:h-12 md:w-12",children:e.jsx(B,{className:"h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5"})})]})]})})]})}function fe(){const d=[{stars:5,text:"Since implementing Court Matchup, our club's revenue has increased by 25% and member satisfaction is at an all-time high. The automated scheduling eliminated double bookings completely.",name:"Nathan Johnston",title:"Club Director at Ace Tennis Club",avatar:"/images/landing/tennis-player.jpg"},{stars:5,text:"My client base grew 40% in just three months! The availability management lets me set my schedule once and forget it, while clients book lessons without any back-and-forth emails.",name:"Sarah Williams",title:"Head Coach at Grand Slam Academy",avatar:"/images/landing/female-tennis-player.jpg"},{stars:5,text:"I moved to a new city and was worried about finding partners. Court Matchup matched me with 5 regular hitting partners at my skill level within weeks. My game has improved tremendously!",name:"Michael Chen",title:"Member at Riverside Tennis Club",avatar:"/images/landing/male-tennis-player3.jpg"},{stars:5,text:"We reduced payment processing time by 75% and eliminated $12,000 in annual administrative costs. The reporting tools give us insights we never had access to before.",name:"Jennifer Rodriguez",title:"Operations Manager at City Sports Complex",avatar:"/images/landing/female-tennis-player.jpg"},{stars:5,text:"Our annual tournament registration increased by 60% this year. Players love the seamless sign-up process, and our staff saved countless hours on administrative tasks.",name:"David Thompson",title:"Tournament Director at Regional Tennis Association",avatar:"/images/landing/male-tennis-player.jpg"}],[f,w]=p.useState(0),[a,N]=p.useState(window.innerWidth),[o,j]=p.useState(!1),[k,b]=p.useState(null),{ref:z,inView:v}=re({threshold:.3,triggerOnce:!1}),t=(()=>a<640?1:a<1024?2:3)();p.useEffect(()=>{const n=()=>{N(window.innerWidth)};return window.addEventListener("resize",n),()=>window.removeEventListener("resize",n)},[]);const l=()=>{o||(j(!0),b("left"),setTimeout(()=>{w(n=>n===0?d.length-t:n-1),j(!1)},300))},h=()=>{o||(j(!0),b("right"),setTimeout(()=>{w(n=>n+t>=d.length?0:n+1),j(!1)},300))},m=d.slice(f,f+t);for(;m.length<t;)m.push(d[(f+m.length)%d.length]);const y={hidden:()=>({opacity:0,y:50,x:k==="left"?50:k==="right"?-50:0}),visible:n=>({opacity:1,y:0,x:0,transition:{type:"spring",damping:12,stiffness:100,duration:.5,delay:n*.1}}),exit:()=>({opacity:0,x:k==="left"?-50:50,transition:{duration:.3}})},g={hidden:{scale:0},visible:n=>({scale:1,transition:{delay:.3+n*.1,type:"spring",stiffness:200}})};return e.jsxs("div",{className:"w-full overflow-hidden",ref:z,children:[e.jsx("div",{className:"grid w-full grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 lg:gap-8",children:m.map((n,C)=>e.jsxs(x.div,{custom:C,initial:"hidden",animate:v||o?"visible":"hidden",exit:"exit",variants:y,whileHover:{y:-10,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"},className:"mx-auto flex w-full flex-col rounded-2xl bg-white p-3 shadow-xl transition-shadow duration-300 sm:p-5 md:p-6",children:[e.jsx("div",{className:"mb-2 flex sm:mb-3",children:[...Array(n.stars)].map((L,H)=>e.jsx(x.div,{custom:H,variants:g,initial:"hidden",animate:v?"visible":"hidden",children:e.jsx(ee,{className:"h-3 w-3 text-yellow-400 sm:h-4 sm:w-4"})},H))}),e.jsx(x.p,{initial:{opacity:0},animate:v?{opacity:1}:{opacity:0},transition:{delay:.4,duration:.5},className:"mb-3 text-xs text-gray-700 sm:mb-4 sm:text-sm",children:n.text}),e.jsxs(x.div,{initial:{opacity:0,y:20},animate:v?{opacity:1,y:0}:{opacity:0,y:20},transition:{delay:.5,duration:.5},className:"mt-auto flex items-center",children:[e.jsx(x.img,{whileHover:{scale:1.1,rotate:5},src:n.avatar,alt:n.name,className:"mr-2 h-10 w-10 rounded-full border-2 border-[#005954] object-cover sm:mr-3 sm:h-12 sm:w-12",loading:"lazy"}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-bold text-gray-900 sm:text-base",children:n.name}),e.jsx("p",{className:"text-xs text-gray-500",children:n.title})]})]})]},`${f}-${C}`))}),e.jsxs("div",{className:"mt-8 flex justify-between sm:mt-10",children:[e.jsx(x.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"flex h-10 w-10 items-center justify-center rounded-lg bg-[#24443e] text-white shadow-lg transition-colors hover:bg-[#003530] sm:h-12 sm:w-12",onClick:l,"aria-label":"Previous testimonial",disabled:o,children:e.jsx(E,{className:"h-4 w-4 sm:h-5 sm:w-5"})}),e.jsx(x.button,{whileHover:{scale:1.1},whileTap:{scale:.9},className:"flex h-10 w-10 items-center justify-center rounded-lg bg-[#24443e] text-white shadow-lg transition-colors hover:bg-[#003530] sm:h-12 sm:w-12",onClick:h,"aria-label":"Next testimonial",disabled:o,children:e.jsx(B,{className:"h-4 w-4 sm:h-5 sm:w-5"})})]})]})}export{ts as default};
