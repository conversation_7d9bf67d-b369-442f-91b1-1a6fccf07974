import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{r as i}from"./vendor-851db8c1.js";import{A as o}from"./index-9d1f2a50.js";import{T as t}from"./index-f5718a4d.js";import"./index-9620a837.js";import{L as p}from"./index-3b44b02c.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./cal-heatmap-cf010ec4.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const e=({children:m})=>r.jsx(p,{children:r.jsx("div",{id:"admin_wrapper",className:"flex w-full max-w-full flex-col bg-white",children:r.jsxs("div",{className:"flex min-h-screen w-full max-w-full ",children:[r.jsx(o,{}),r.jsxs("div",{className:"w-full overflow-hidden",children:[r.jsx(t,{}),r.jsx(i.Suspense,{fallback:r.jsx("div",{className:"flex h-screen w-full items-center justify-center"}),children:r.jsx("div",{className:"w-full overflow-y-auto overflow-x-hidden",children:m})})]})]})})}),J=i.memo(e);export{J as default};
