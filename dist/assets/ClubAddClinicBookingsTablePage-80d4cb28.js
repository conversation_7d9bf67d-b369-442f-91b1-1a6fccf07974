import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as k}from"./vendor-851db8c1.js";import{u as w}from"./react-hook-form-687afde5.js";import{o as A}from"./yup-2824f222.js";import{c as C,a as n}from"./yup-54691517.js";import{G as E,A as I,d as N,M as v,b as D,t as P}from"./index-3b44b02c.js";import"./react-quill-73fb9518.js";/* empty css                   */import{M as p}from"./MkdInput-155433e6.js";import"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const jt=({setSidebar:h})=>{const{dispatch:c}=a.useContext(E),x=C({clinic_id:n(),user_id:n(),status:n(),payment_status:n()}).required(),{dispatch:y}=a.useContext(I),[f,R]=a.useState({}),[b,d]=a.useState(!1),S=k(),{register:r,handleSubmit:j,setError:g,setValue:T,formState:{errors:m}}=w({resolver:A(x)});a.useState([]);const _=async i=>{let u=new v;d(!0);try{for(let o in f){let s=new FormData;s.append("file",f[o].file);let l=await u.uploadImage(s);i[o]=l.url}u.setTable("clinic_bookings");const t=await u.callRestAPI({clinic_id:i.clinic_id,user_id:i.user_id,status:i.status,payment_status:i.payment_status},"POST");if(!t.error)D(c,"Added"),S("/club/clinic_bookings"),h(!1),c({type:"REFRESH_DATA",payload:{refreshData:!0}});else if(t.validation){const o=Object.keys(t.validation);for(let s=0;s<o.length;s++){const l=o[s];g(l,{type:"manual",message:t.validation[l]})}}d(!1)}catch(t){d(!1),console.log("Error",t),g("clinic_id",{type:"manual",message:t.message}),P(y,t.message)}};return a.useEffect(()=>{c({type:"SETPATH",payload:{path:"clinic_bookings"}})},[]),e.jsxs("div",{className:" shadow-md rounded  mx-auto p-5",children:[e.jsx("h4",{className:"text-2xl font-medium",children:"Add Clinic Bookings"}),e.jsxs("form",{className:" w-full max-w-lg",onSubmit:j(_),children:[e.jsx(p,{type:"number",page:"add",name:"clinic_id",errors:m,label:"Clinic Id",placeholder:"Clinic Id",register:r,className:""}),e.jsx(p,{type:"number",page:"add",name:"user_id",errors:m,label:"User Id",placeholder:"User Id",register:r,className:""}),e.jsx(p,{page:"add",name:"status",errors:m,label:"Status",placeholder:"Status",register:r,className:""}),e.jsx(p,{page:"add",name:"payment_status",errors:m,label:"Payment Status",placeholder:"Payment Status",register:r,className:""}),e.jsx(N,{type:"submit",loading:b,disabled:b,className:"bg-primaryBlue text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:"Submit"})]})]})};export{jt as default};
