import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as $e,L as Ve}from"./vendor-851db8c1.js";import{M as He,T as Ze,aU as Re,G as Ie,A as Oe,c as Fe,E as S,H as _,J as E,b as g,K as Be,R as qe,t as P}from"./index-3b44b02c.js";import{c as ze,a as te}from"./yup-54691517.js";import{u as Je}from"./react-hook-form-687afde5.js";import{o as Ue}from"./yup-2824f222.js";import{a as We}from"./index.esm-9c6194ba.js";import{P as Ye}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import Ge from"./Skeleton-1e8bf077.js";import"./numeral-ea653b2a.js";import{T as Ke}from"./TimeslotPicker-06293178.js";import{D as Qe}from"./DataTable-a2248415.js";import{H as Xe}from"./HistoryComponent-05a07961.js";import{R as et}from"./RoleAccessManager-6c63c7e7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";import"./BottomDrawer-f0d615b3.js";import"./TimeSlotGrid-3140c36d.js";const tt=m=>{if(!m)return"--";const h=m.replace(/\D/g,"");if(h.length<10)return m;const f=h.match(/^(\d{1})(\d{3})(\d{3})(\d{4})$/);if(f)return`(${f[2]}) ${f[3]}-${f[4]}`;if(h.length===10){const x=h.match(/^(\d{3})(\d{3})(\d{4})$/);return`(${x[1]}) ${x[2]}-${x[3]}`}return m};let n=new He,st=new Ze;const at=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],it=()=>{var Q,X;const{dispatch:m,state:h}=a.useContext(Ie),{dispatch:f}=a.useContext(Oe),[x,se]=a.useState([]),[c,k]=a.useState(10),[ae,ie]=a.useState(0),[rt,re]=a.useState(0),[d,le]=a.useState(1),[oe,ne]=a.useState(!1),[ce,de]=a.useState(!1),[lt,me]=a.useState(!1),[D,j]=a.useState(!0),[ue,ot]=a.useState(!1),[fe,nt]=a.useState(!1),[$,M]=a.useState(!1),[A,y]=a.useState([]),[r,V]=a.useState(null),[pe,H]=a.useState(!1);a.useState([]);const[ge,Z]=a.useState(!1),[he,R]=a.useState(!1),[xe,ye]=a.useState(null),[I,be]=a.useState(!1),[O,F]=a.useState(""),[B,ve]=a.useState(null),[Ce,q]=a.useState(!1),[z,L]=a.useState([]),[Se,N]=a.useState(!1),[J,U]=a.useState(!1),je=localStorage.getItem("role"),w=localStorage.getItem("user");$e();const W=a.useRef(null),Me=ze().shape({email:te().email("Invalid email").required("Email is required"),password:te().required("Password is required").min(6,"Password must be at least 6 characters")}),{register:Y,handleSubmit:Ae,reset:Le,formState:{errors:b}}=Je({resolver:Ue(Me)});function Ne(){u(d-1,c)}function we(){u(d+1,c)}async function u(e,s,i={},o=[]){j(!(fe||ue));try{const l=await st.getPaginate("admin_staff",{page:e,limit:s,filter:[...o],join:["user|user_id"]});l&&j(!1);const{list:p,total:v,limit:C,num_pages:ee,page:T}=l;se(p),k(C),ie(ee),le(T),re(v),ne(T>1),de(T+1<=ee)}catch(l){j(!1),console.log("ERROR",l),P(f,l.message)}}a.useEffect(()=>{m({type:"SETPATH",payload:{path:"admin_staff"}});const s=setTimeout(async()=>{await u(d,c,{})},700);return()=>{clearTimeout(s)}},[d,c]);const G=e=>{W.current&&!W.current.contains(e.target)&&me(!1)};a.useEffect(()=>(document.addEventListener("mousedown",G),()=>{document.removeEventListener("mousedown",G)}),[]);const Te=async({staff_id:e,user_id:s})=>{R(!0);try{n.setTable("user"),await n.callRestAPI({id:s},"DELETE"),n.setTable("staff"),await n.callRestAPI({id:e},"DELETE"),await _(n,{user_id:w,activity_type:S.staff_management,action_type:E.DELETE,data:{staff_id:e,staff_user_id:s},description:"Deleted staff"}),g(m,"Staff deleted successfully",3e3,"success"),u(d,c)}catch(i){console.error("Error deleting staff:",i),g(m,i.message,3e3,"error"),P(f,i.message)}R(!1)},_e=async e=>{U(!0);try{const s=await n.callRawAPI("/v3/api/custom/courtmatchup/staff/register",{email:e.email,password:e.password,role:"admin_staff",verify:1,status:1,club_id:0},"POST");await n.sendMail({email:e.email,subject:"Admin Staff Login Credentials",body:`Your login credentials are: <br> Email: ${e.email} <br> Password: ${e.password} <br><br> You can log in at: <a href="${window.location.origin}/admin_staff/login" style="color: #1D275F; text-decoration: underline;">Admin Staff Login Portal</a>`}),await _(n,{user_id:w,activity_type:S.staff_management,action_type:E.CREATE,data:{email:e.email},description:"Added new admin staff"}),g(m,"Admin staff added successfully",3e3,"success"),Le(),N(!1),u(d,c)}catch(s){console.error("Error adding admin staff:",s),g(m,s.message,3e3,"error"),P(f,s.message)}U(!1)},Ee=e=>{if(V(e),M(!0),e.availability)try{const s=JSON.parse(e.availability),i=at.map(o=>({day:o.toLowerCase(),timeslots:[]}));s&&Array.isArray(s)&&s.length>0&&s.forEach(o=>{const l=i.findIndex(p=>p.day===o.day.toLowerCase());l!==-1&&(i[l].timeslots=o.timeslots)}),y(i)}catch(s){console.error("Error parsing availability:",s),y([])}else y([])};a.useEffect(()=>{const e=s=>{I&&!s.target.closest("#contact-info-button")&&!s.target.closest(".contact-info-popover")&&be(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[I]);const Pe=e=>{const s=e.target.value;F(s),B&&clearTimeout(B);const i=setTimeout(()=>{s.trim()?u(d,c,{},[`${n._project_id}_user.first_name,cs,${s}`]):u(d,c)},500);ve(i)},K=async()=>{try{n.setTable("club_permissions");const e=await n.callRestAPI({filter:["club_id,eq,0"]},"GETALL");e.list.length>0?L(e.list[0]):L(null)}catch(e){console.error("Error fetching role access data:",e),L(null)}};a.useEffect(()=>{K()},[]);const ke={name:e=>{var s,i,o,l,p,v,C;return t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("img",{src:((s=e.user)==null?void 0:s.photo)||"/default-avatar.png",alt:`${(i=e.user)==null?void 0:i.first_name} ${(o=e.user)==null?void 0:o.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),t.jsx("span",{className:"font-medium capitalize text-gray-900",children:!((l=e==null?void 0:e.user)!=null&&l.first_name)||!((p=e==null?void 0:e.user)!=null&&p.last_name)?"--":`${(v=e==null?void 0:e.user)==null?void 0:v.first_name} ${(C=e==null?void 0:e.user)==null?void 0:C.last_name}`})]})},status:e=>{var s;return t.jsx("span",{className:"text-gray-600",children:((s=e==null?void 0:e.user)==null?void 0:s.status)===1?"Active":"Inactive"})},role:e=>{var s,i;return t.jsx("span",{className:"capitalize text-gray-600",children:(s=e==null?void 0:e.user)!=null&&s.role?(i=e==null?void 0:e.user)==null?void 0:i.role:"--"})},email:e=>{var s;return t.jsx("span",{className:"text-gray-600",children:((s=e==null?void 0:e.user)==null?void 0:s.email)||"--"})},phone:e=>{var s;return t.jsx("span",{className:"text-gray-600",children:(s=e==null?void 0:e.user)!=null&&s.phone?tt(e.user.phone):"--"})},bank_details:e=>t.jsx("span",{className:"text-gray-600",children:(()=>{var s;try{if(e!=null&&e.account_details){const i=JSON.parse(e.account_details);return i&&Array.isArray(i)&&i.length>0&&((s=i[0])!=null&&s.account_number)?`•••• ${i[0].account_number.slice(-4)}`:"--"}return"--"}catch{return"--"}})()}),actions:e=>t.jsxs("div",{className:"flex items-center justify-end gap-3",children:[t.jsx(Ve,{to:`/${je}/view-staff/${e.id}`,className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M6.88016 17.7625C7.97481 16.1634 9.77604 15.1243 11.9993 15.1243C14.2227 15.1243 16.0239 16.1634 17.1185 17.7625M6.88016 17.7625C8.24153 18.9726 10.0346 19.7077 11.9993 19.7077C13.9641 19.7077 15.7572 18.9726 17.1185 17.7625M6.88016 17.7625C5.29173 16.3505 4.29102 14.2918 4.29102 11.9993C4.29102 7.74215 7.74215 4.29102 11.9993 4.29102C16.2565 4.29102 19.7077 7.74215 19.7077 11.9993C19.7077 14.2918 18.707 16.3505 17.1185 17.7625M14.7077 10.3327C14.7077 11.8285 13.4951 13.041 11.9993 13.041C10.5036 13.041 9.29102 11.8285 9.29102 10.3327C9.29102 8.83691 10.5036 7.62435 11.9993 7.62435C13.4951 7.62435 14.7077 8.83691 14.7077 10.3327Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinejoin:"round"})})}),t.jsx("button",{onClick:()=>Ee(e),className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M2.46209 9.95722L3.76445 17.3433C3.86035 17.8872 4.37901 18.2503 4.9229 18.1544L10.1162 17.2387M2.46209 9.95722L1.94114 7.0028C1.84524 6.45891 2.20841 5.94025 2.7523 5.84434L14.0776 3.84739C14.6215 3.75149 15.1401 4.11466 15.236 4.65855L15.757 7.61297L2.46209 9.95722ZM16.0002 11.7509V14.0009L18.0002 16.0009M22.2502 14.0009C22.2502 17.4527 19.452 20.2509 16.0002 20.2509C12.5485 20.2509 9.75025 17.4527 9.75025 14.0009C9.75025 10.5491 12.5485 7.75092 16.0002 7.75092C19.452 7.75092 22.2502 10.5491 22.2502 14.0009Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),t.jsx("button",{onClick:()=>{Z(!0),ye({staff_id:e.id,user_id:e.user_id})},className:"rounded-full p-2 hover:bg-gray-100",children:t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},De=[{header:"Name",accessor:"name"},{header:"Status",accessor:"status"},{header:"Role",accessor:"role"},{header:"Email",accessor:"email"},{header:"Phone",accessor:"phone"},{header:"Bank details",accessor:"bank_details"},{header:"Actions",accessor:"actions"}];return console.log("roleAccessData",z),t.jsxs("div",{className:"h-screen p-5",children:[t.jsxs("div",{className:"flex w-full flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[t.jsx("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:gap-4",children:t.jsxs("div",{className:"relative flex max-w-md flex-1 items-center",children:[t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx(Fe,{className:"text-gray-500"})}),t.jsx("input",{type:"text",value:O,onChange:Pe,className:"block w-full min-w-[200px] rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search staff by name or email"}),O&&t.jsx("button",{onClick:()=>{F(""),u(d,c)},className:"absolute right-2 rounded-full p-1 hover:bg-gray-100",children:t.jsx(We,{className:"text-gray-500"})})]})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsxs("button",{onClick:()=>q(!0),className:"inline-flex items-center gap-2 rounded-lg border border-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50",children:[t.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M4.75038 16.8788V11.6684M4.75038 8.54214V3.12305M9.9992 16.6703V10.6259M9.99922 7.49978V3.33138M15.248 16.8785V13.3354M15.248 10.2095V3.12305M3.12109 11.46H6.45753M8.33253 7.70801H11.6659M13.5409 13.1271H16.8742",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),"Manage role access"]}),t.jsxs("button",{onClick:()=>N(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[t.jsx("span",{className:"",children:"+"}),"Add new"]})]}),t.jsx("div",{className:" ",children:t.jsx(Xe,{title:"Staff History",emptyMessage:"No staff history found",activityType:S.staff_management})})]}),D?t.jsx(Ge,{}):t.jsx(Qe,{columns:De,data:x,loading:D,renderCustomCell:ke,emptyMessage:"No staff members found",loadingMessage:"Loading staff members..."}),t.jsx(Ye,{currentPage:d,pageCount:ae,pageSize:c,canPreviousPage:oe,canNextPage:ce,updatePageSize:e=>{k(e),u(d,e)},previousPage:Ne,nextPage:we,gotoPage:e=>u(e,c)}),$&&r&&t.jsx(Ke,{showTimesAvailableModal:$,setShowTimesAvailableModal:M,selectedTimes:A,setSelectedTimes:y,title:`${(Q=r==null?void 0:r.user)==null?void 0:Q.first_name} ${(X=r==null?void 0:r.user)==null?void 0:X.last_name}'s availability`,isSubmitting:pe,onSave:async e=>{var s,i;try{H(!0);const l=(()=>A?A.filter(p=>p.timeslots.length>0):[])();n.setTable("staff"),await n.callRestAPI({id:r.id,availability:JSON.stringify(l)},"PUT"),await _(n,{user_id:w,activity_type:S.staff_management,action_type:E.UPDATE,data:{staff_id:r.id,availability:l},description:`Updated availability for ${(s=r==null?void 0:r.user)==null?void 0:s.first_name} ${(i=r==null?void 0:r.user)==null?void 0:i.last_name}`}),g(m,"Availability updated successfully",3e3,"success"),u(d,c)}catch(o){console.error("Error saving availability:",o),g(f,o.message,3e3,"error")}finally{H(!1),M(!1),V(null)}}}),t.jsx(Be,{isOpen:ge,onClose:()=>Z(!1),onDelete:()=>Te(xe),message:"Are you sure you want to delete this staff?",loading:he,title:"Delete Staff"}),t.jsx(qe,{isOpen:Se,onClose:()=>N(!1),title:"Add New Admin Staff",primaryButtonText:"Add Staff",onPrimaryAction:Ae(_e),submitting:J,primaryButtonDisabled:J,children:t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsxs("div",{className:"flex flex-col gap-1",children:[t.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email"}),t.jsx("input",{id:"email",type:"email",...Y("email"),className:"rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter email address"}),b.email&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:b.email.message})]}),t.jsxs("div",{className:"flex flex-col gap-1",children:[t.jsx("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),t.jsx("input",{id:"password",type:"password",...Y("password"),className:"rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Enter password"}),b.password&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:b.password.message})]}),t.jsx("p",{className:"mt-2 text-xs text-gray-500",children:"After adding the staff member, they will be able to log in with these credentials. You can manage their permissions from the role access settings."})]})}),t.jsx(et,{isOpen:Ce,onClose:()=>{q(!1),K()},roleAccessData:z,club:{id:0},roleLabel:"Admin Staff"})]})},ts=()=>t.jsx(Re,{restrictionMessage:"You don't have permission to access admin staff management. This module is disabled by default and can be enabled by an admin.",children:t.jsx(it,{})});export{ts as default};
