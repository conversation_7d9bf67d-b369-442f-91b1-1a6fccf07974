import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,b as y}from"./vendor-851db8c1.js";import{G as xl,u as fl,A as gl,c as bl,q as Y,N as hl,ai as yl,V as wl,aj as kl,M as jl,T as _l,i as Nl}from"./index-3b44b02c.js";import{H as Sl}from"./HistoryComponent-05a07961.js";import{f as Cl}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Z=new jl,rl=new _l;function ut(){var T,$,M;const[v,ll]=o.useState([]);o.useState(!1),y.useContext(xl);const{club:d,sports:i,courts:m,pricing:tl}=fl();y.useContext(gl);const[f,w]=o.useState(null),[sl,k]=y.useState(!1),[el,j]=o.useState(!1),[e,_]=o.useState(null),[p,g]=o.useState(null),[a,x]=o.useState(null),[N,S]=o.useState(!1),[C,r]=o.useState(!1),[cl,dl]=o.useState([]),[D,E]=o.useState("");console.log({club:d,courts:m,sports:i});async function u({filters:t}){k(!0);try{const c=await rl.getList("reservation",{filter:[...t],join:["booking|booking_id","user|user_id","buddy|buddy_id","clubs|club_id"]});console.log("reservations response",c);const b=c.list.map(l=>{var A,L,F,O,R,B,I,G,H,z,J,K,Q,U,q,P,V,W,X;return{id:(A=l==null?void 0:l.booking)==null?void 0:A.id,court_ids:(L=l==null?void 0:l.booking)!=null&&L.court_ids?JSON.parse((F=l==null?void 0:l.booking)==null?void 0:F.court_ids):(O=l==null?void 0:l.booking)==null?void 0:O.court_id,startTime:(R=l==null?void 0:l.booking)==null?void 0:R.start_time,endTime:(B=l==null?void 0:l.booking)==null?void 0:B.end_time,date:(I=l==null?void 0:l.booking)==null?void 0:I.date,reservation_type:(G=l==null?void 0:l.booking)==null?void 0:G.reservation_type,title:l!=null&&l.buddy_id?"Buddy":(H=Nl.find(h=>{var n;return h.value==((n=l==null?void 0:l.booking)==null?void 0:n.reservation_type)}))==null?void 0:H.label,status:(z=l==null?void 0:l.booking)==null?void 0:z.status,price:(J=l==null?void 0:l.booking)==null?void 0:J.price,duration:(K=l==null?void 0:l.booking)==null?void 0:K.duration,players:(Q=l==null?void 0:l.booking)!=null&&Q.player_ids?JSON.parse((U=l==null?void 0:l.booking)==null?void 0:U.player_ids):[],sport:i.find(h=>{var n;return h.id==((n=l==null?void 0:l.booking)==null?void 0:n.sport_id)}),user_id:(q=l==null?void 0:l.booking)==null?void 0:q.user_id,booking:l==null?void 0:l.booking,user:l==null?void 0:l.user,buddy:l==null?void 0:l.buddy,club:l==null?void 0:l.clubs,type:(P=l==null?void 0:l.booking)==null?void 0:P.type,sub_type:(V=l==null?void 0:l.booking)==null?void 0:V.subtype,court_fee:(W=l==null?void 0:l.booking)==null?void 0:W.court_fee,sport_id:(X=l==null?void 0:l.booking)==null?void 0:X.sport_id,buddy_id:l==null?void 0:l.buddy_id}});ll(b)}catch(c){console.error("Error fetching data:",c)}finally{k(!1)}}const ol=t=>{_(t),g(null),x(null);const c=`courtmatchup_booking.sport_id,cs,${t==null?void 0:t.id}`;u({filters:[c]})},ul=t=>{g(t),x(null),S(!1);const c=[`courtmatchup_booking.sport_id,cs,${e==null?void 0:e.id}`,`courtmatchup_booking.type,cs,${t}`];u({filters:c})},il=t=>{x(t),r(!1);const c=[`courtmatchup_booking.sport_id,cs,${e==null?void 0:e.id}`,`courtmatchup_booking.type,cs,${p}`,`courtmatchup_booking.subtype,cs,${t}`];u({filters:c})},ml=async()=>{try{Z.setTable("user");const t=await Z.callRestAPI({},"GETALL");dl(t.list),console.log("users",t)}catch(t){console.log(t)}};o.useEffect(()=>{if(d!=null&&d.id){const t=Cl(new Date,"yyyy-MM-dd");u({filters:[`courtmatchup_booking.date,cs,${t}`]}),ml()}},[d==null?void 0:d.id]);const pl=()=>{j(!0)},nl=t=>{w(t)},al=()=>{_(null),g(null),x(null),E(""),u({filters:[]})};return console.log("courts",m),s.jsxs("div",{className:"h-full rounded-lg bg-white p-4",children:[s.jsx("div",{className:"flex w-full flex-col",children:s.jsxs("div",{className:"mb-6 flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0",children:[s.jsx("div",{className:"w-full lg:w-auto lg:min-w-[300px] lg:max-w-[400px]",children:s.jsxs("div",{className:"relative flex items-center",children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(bl,{className:"h-5 w-5 text-gray-500"})}),s.jsx("input",{type:"text",value:D,onChange:t=>{E(t.target.value);const c=e?[`courtmatchup_booking.sport_id,cs,${e==null?void 0:e.id}`]:[],b=t.target.value?[`courtmatchup_user.first_name,cs,${t.target.value}`]:[];u({filters:[...c,...b]})},className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search players by name..."})]})}),s.jsxs("div",{className:"flex flex-col space-y-4 lg:flex-row lg:items-start lg:space-x-4 lg:space-y-0",children:[s.jsxs("div",{className:"flex flex-col space-y-3",children:[s.jsx("div",{className:"flex max-w-fit flex-wrap gap-2 rounded-lg border bg-white p-2 sm:flex-nowrap sm:gap-0 sm:overflow-hidden sm:p-0",children:(i==null?void 0:i.length)>0&&i.filter(t=>t.status===1).map((t,c)=>s.jsx("button",{className:`whitespace-nowrap rounded px-3 py-2 text-sm font-medium transition-colors sm:rounded-none ${(e==null?void 0:e.id)===(t==null?void 0:t.id)?"bg-primaryBlue text-white":"text-gray-600 hover:bg-gray-100"}`,onClick:()=>ol(t),children:t==null?void 0:t.name},c))}),s.jsxs("div",{className:"flex flex-wrap gap-2 sm:flex-nowrap sm:space-x-2",children:[e&&s.jsxs("div",{className:"relative min-w-[140px]",children:[s.jsxs("button",{onClick:()=>S(!N),className:"flex w-full items-center justify-between space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[s.jsx("span",{className:"truncate",children:p||"Select Type"}),s.jsx(Y,{className:"h-4 w-4 flex-shrink-0"})]}),N&&s.jsx("div",{className:"absolute left-0 z-10 mt-1 w-full min-w-[180px] rounded-lg border bg-white shadow-lg",children:(T=e==null?void 0:e.sport_types)==null?void 0:T.map((t,c)=>s.jsx("button",{className:"block w-full px-4 py-2 text-left text-sm hover:bg-gray-100",onClick:()=>ul(t.type),children:t.type},c))})]}),p&&s.jsxs("div",{className:"relative min-w-[140px]",children:[s.jsxs("button",{onClick:()=>r(!C),className:"flex w-full items-center justify-between space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[s.jsx("span",{className:"truncate",children:a||"Select Subtype"}),s.jsx(Y,{className:"h-4 w-4 flex-shrink-0"})]}),C&&s.jsx("div",{className:"absolute left-0 z-10 mt-1 w-full min-w-[180px] rounded-lg border bg-white shadow-lg",children:(M=($=e==null?void 0:e.sport_types)==null?void 0:$.find(t=>t.type===p))==null?void 0:M.subtype.map((t,c)=>s.jsx("button",{className:"block w-full px-4 py-2 text-left text-sm hover:bg-gray-100",onClick:()=>il(t),children:t},c))})]})]})]}),s.jsxs("div",{className:"flex flex-wrap gap-2 sm:flex-nowrap sm:space-x-2",children:[(D||e||p||a)&&s.jsxs("button",{onClick:al,className:"flex items-center space-x-2 rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50",children:[s.jsx(hl,{className:"h-4 w-4"}),s.jsx("span",{className:"hidden sm:inline",children:"Clear filters"}),s.jsx("span",{className:"sm:hidden",children:"Clear"})]}),s.jsx(Sl,{title:"Reservation History",emptyMessage:"No reservation history found"}),s.jsxs("button",{onClick:pl,className:"hover:bg-navy-800 flex items-center space-x-2 rounded-lg bg-navy-700 px-3 py-2 text-sm font-medium text-white",children:[s.jsx("span",{children:"+"}),s.jsx("span",{children:"Add event"})]})]})]})]})}),s.jsx(yl,{sports:i,events:v,loading:sl,onDateChange:u,onEventClick:nl,courts:(m==null?void 0:m.filter(t=>!(e&&t.sport_id!==e.id||p&&t.type!==p||a&&t.sub_type!==a)))||[],fetchData:u}),f&&s.jsx(wl,{isOpen:!!f,onClose:()=>w(null),event:f,courts:m||[],users:cl,clubId:d==null?void 0:d.id,sports:i,fetchData:u,club:d,pricing:tl}),(d==null?void 0:d.id)&&s.jsx(kl,{setShowEventModal:j,showEventModal:el,fetchData:u,club:d,sports:i,courts:m})]})}export{ut as default};
