import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,b as S}from"./vendor-851db8c1.js";import{m as he,b as xe,c as C,a as be,r as we}from"./index.esm-09a3a6b8.js";import{G as ye,A as ve,C as _e,a as je,E as Ie,U as Me,I as Se}from"./EditGroupNameModal-a148e6fa.js";import{G as Ce,A as Ae,u as Ee,aC as Ne,e as Re,b as d,R as A,K as q,T as Te,M as Pe,E as K,J as Oe}from"./index-3b44b02c.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-icons-51bc3cff.js";import"./react-hook-form-687afde5.js";import"./yup-2824f222.js";import"./@hookform/resolvers-67648cca.js";import"./yup-54691517.js";import"./react-phone-input-2-57d1f0dd.js";import"./country-state-city-282f4569.js";/* empty css              */import"./react-tooltip-7a26650a.js";import"./@mantine/core-8cbffb6d.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let De=new Te,m=new Pe;function At(){var k;const[c,V]=o.useState([]),{dispatch:l}=S.useContext(Ce);S.useContext(Ae);const[z,b]=o.useState(!1),[B,E]=o.useState(!1),[H,y]=o.useState(!1),[X,g]=o.useState(!1),[Fe,Y]=o.useState(null),[Ge,N]=o.useState(!1),[v,Q]=o.useState([]);o.useState(null);const[W,R]=o.useState(null),[Z,T]=o.useState(!1),[ee,P]=o.useState(!1),[te,_]=o.useState(!1),[re,O]=o.useState(!1),[r,a]=o.useState(null),[p,j]=o.useState(null),[se,I]=o.useState(!1),[oe,D]=o.useState(!1),[ne,F]=o.useState(!1),{club:u,user_profile:h,triggerRefetch:G,user_permissions:x}=Ee(),[ae,w]=o.useState(!1),[Ue,ie]=o.useState([]),[U,le]=o.useState([]),i=parseInt(localStorage.getItem("user")),de=async()=>{try{const s=(await De.getList("user",{filter:["role,cs,user",`club_id,eq,${u==null?void 0:u.id}`]})).list.filter(n=>(n==null?void 0:n.id)==i?!1:n.guardian===i?parseInt(i)===parseInt(n.guardian):!0);Q(s)}catch(e){console.error("Error fetching users:",e)}},L=async()=>{try{const e=await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/pending-invites",{},"GET");ie(e.pending_invites)}catch(e){console.error("Error fetching pending invites:",e)}},$=async()=>{try{const e=await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups/sent-invites",{},"GET");console.log("sent invites",e),!e.error&&e.invites&&le(e.invites)}catch(e){console.error("Error fetching sent invites:",e)}},me=e=>(localStorage.getItem("user"),e.filter(s=>s.guardian===i?parseInt(i)===parseInt(s.guardian):!0)),f=async()=>{E(!0);try{const s=(await m.callRawAPI("/v3/api/custom/courtmatchup/user/groups?type=0",{},"GET")).groups.map(n=>({...n,members:me(n.members||[])}));V(s)}catch(e){console.error("Error fetching users:",e)}finally{E(!1)}};o.useEffect(()=>{f(),L(),$()},[G]),o.useEffect(()=>{de()},[G]),S.useEffect(()=>{l({type:"SETPATH",payload:{path:"my-groups"}})},[]);const ue=e=>{const s=c.find(n=>n.id===e||n.group_id===e);if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can add members",3e3,"error");return}Y(e),b(!0)},ce=e=>{const s=c.flatMap(n=>n.members).find(n=>n.id===e);s&&(R(s),T(!0))},pe=async()=>{var e;if(r&&r.group_owner_id&&parseInt(r.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can remove members",3e3,"error");return}D(!0);try{const n=((e=r.members)==null?void 0:e.map(M=>M.id)).filter(M=>M!==p.id),J={id:r.id,members:JSON.stringify(n)};m.setTable("user_groups");const Le=await m.callRestAPI(J,"PUT");m.setTable("activity_logs"),await m.callRestAPI({user_id:i,action:"Removed member from group",activity_type:K.group,action_type:Oe.DELETE,data:JSON.stringify(J),club_id:u==null?void 0:u.id,description:"Removed member from group"},"POST"),a(null),j(null),I(!1),d(l,"Member removed successfully"),f()}catch(s){console.error("Error removing member:",s),d(l,s.message,3e3,"error")}finally{D(!1)}},fe=async()=>{if(r&&r.group_owner_id&&parseInt(r.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can delete the group",3e3,"error");return}O(!0);try{m.setTable("user_groups"),await m.callRestAPI({id:r.group_id},"DELETE"),m.setTable("activity_logs"),await m.callRestAPI({user_id:i,action:"Deleted group",type:K.delete_group,data:JSON.stringify({group:r}),club_id:u==null?void 0:u.id},"POST"),_(!1),a(null),f()}catch(e){console.error("Error deleting group:",e)}finally{O(!1)}},ge=async e=>{N(!0);try{console.log("Adding family member:",e),await new Promise(s=>setTimeout(s,1e3)),g(!1)}catch(s){console.error("Error adding family member:",s)}finally{N(!1)}};return x&&!x.allowGroups?t.jsx(Ne,{message:`Your current plan (${x==null?void 0:x.planName}) does not include groups feature. Please upgrade your plan to access this feature.`}):t.jsxs("div",{children:[B&&t.jsx(Re,{}),t.jsxs("div",{className:"flex items-center justify-between bg-white px-4 py-4",children:[t.jsx("h1",{className:"mb-6 text-2xl font-semibold",children:"My groups"}),t.jsx("button",{onClick:()=>y(!0),className:"rounded-lg bg-primaryBlue px-3 py-2 text-white",children:"Create group"})]}),t.jsxs("div",{className:"mx-auto mt-5 max-w-7xl p-5",children:[t.jsx("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:(c==null?void 0:c.length)>0?c==null?void 0:c.map(e=>t.jsx(ye,{group:e,currentUserId:i,sentInvites:U,onAddExistingUser:()=>{if(e&&e.group_owner_id&&parseInt(e.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can add members",3e3,"error");return}a(e),w(!0)},onAddMember:ue,onEditName:()=>{if(e&&e.group_owner_id&&parseInt(e.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can edit the group name",3e3,"error");return}F(!0),a(e)},onDeleteGroup:()=>{if(e&&e.group_owner_id&&parseInt(e.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can delete the group",3e3,"error");return}_(!0),a(e)},onViewProfile:ce,onRemoveMember:(s,n)=>{if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can remove members",3e3,"error");return}I(!0),j(n),a(s)},onAddFamilyMember:()=>{if(e&&e.group_owner_id&&parseInt(e.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can add family members",3e3,"error");return}g(!0),a(e)},onInviteToCourtmatch:()=>{if(e&&e.group_owner_id&&parseInt(e.group_owner_id)!==parseInt(i)){d(l,"Only the group owner can invite to CourtMatch",3e3,"error");return}P(!0),a(e)}},e.id)):t.jsx("div",{className:"col-span-3",children:t.jsx("p",{className:"text-center text-gray-500",children:"No groups found"})})}),z&&t.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:t.jsxs("div",{className:"w-96 rounded-lg bg-white p-6",children:[t.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[t.jsx("h3",{className:"text-lg font-medium",children:"Add Member"}),t.jsx("button",{onClick:()=>b(!1),className:"rounded hover:bg-gray-100",children:t.jsx(he,{className:"h-5 w-5"})})]}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("button",{className:"flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50",onClick:()=>{b(!1),w(!0),a(r)},children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(xe,{className:"mr-2"}),t.jsx("span",{children:"Add existing user"})]}),t.jsx(C,{})]}),t.jsxs("button",{className:"flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50",onClick:()=>{b(!1),g(!0),a(r)},children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(be,{className:"mr-2"}),t.jsx("span",{children:"Add family member"})]}),t.jsx(C,{})]}),t.jsxs("button",{className:"flex w-full items-center justify-between rounded-lg border p-3 hover:bg-gray-50",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(we,{className:"mr-2"}),t.jsx("span",{children:"Invite to CourtMatch"})]}),t.jsx(C,{})]})]})]})}),t.jsx(A,{isOpen:X,title:"Add family member",showFooter:!1,onClose:()=>{g(!1),a(null)},children:t.jsx(ve,{users:v,user:h,fetchData:f,group:r,onSubmit:ge,onClose:()=>{g(!1),a(null)}})}),t.jsx(A,{isOpen:H,onClose:()=>y(!1),title:"Create group",showFooter:!1,children:t.jsx(_e,{users:v,fetchData:f,user:h,onClose:()=>y(!1)})}),t.jsx(A,{isOpen:ae,onClose:()=>{w(!1),a(null)},showFooter:!1,title:`Add user to ${r==null?void 0:r.group_name}`,children:t.jsx(je,{user:h,users:v,group:r,fetchData:()=>{L(),$()},sentInvites:U,onClose:()=>{w(!1),a(null)}})}),ne&&t.jsx(Ie,{title:`Edit ${r==null?void 0:r.group_name}`,group:r,user:h,onClose:e=>{F(!1),a(null),e&&f()}}),t.jsx(Me,{isOpen:Z,onClose:()=>{T(!1),R(null)},fetchData:f,user:W}),ee&&t.jsx(Se,{user:h,title:"Invite a friend to Court Matchup",onClose:()=>{P(!1),a(null)},group:r}),t.jsx(q,{isOpen:te,onClose:()=>_(!1),title:"Delete group",requireConfirmation:!0,message:`Are you sure you want to delete ${r==null?void 0:r.group_name} with ${(k=r==null?void 0:r.members)==null?void 0:k.length} members?`,onDelete:fe,buttonText:"Delete",loading:re}),t.jsx(q,{isOpen:se,onClose:()=>{I(!1),a(null),j(null)},title:"Remove member",message:`Are you sure you want to remove ${p==null?void 0:p.first_name} ${p==null?void 0:p.last_name} from ${r==null?void 0:r.group_name}?`,onDelete:pe,requireConfirmation:!0,buttonText:"Remove",loading:oe})]})]})}export{At as default};
