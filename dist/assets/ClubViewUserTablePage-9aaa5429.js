import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,b as l,f as Ae,j as Ke}from"./vendor-851db8c1.js";import{v as ge,M as ye,G as fe,A as Xe,X as Qe,Y as ke,t as je,al as We,e as es,d as k,R as re,K as ue,b as M,u as ss}from"./index-3b44b02c.js";import{B as ts}from"./BottomDrawer-f0d615b3.js";import{M as as}from"./MembershipCard-1e8edd09.js";import{c as ls,a as ie}from"./yup-54691517.js";import{u as ns}from"./react-hook-form-687afde5.js";import{o as rs}from"./yup-2824f222.js";import{P as is}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import os from"./Skeleton-1e8bf077.js";import{G as ds,A as cs,C as ms,a as xs,E as ps,I as hs}from"./EditGroupNameModal-a148e6fa.js";import{F as us}from"./FormattedPhoneNumber-40dd7178.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";import"./index.esm-09a3a6b8.js";import"./react-phone-input-2-57d1f0dd.js";import"./country-state-city-282f4569.js";/* empty css              */import"./react-tooltip-7a26650a.js";const gs=({oldPlan:j,newPlan:d,onClose:m,user:t})=>(console.log("oldPlan",j),console.log("newPlan",d),e.jsx("div",{className:"mx-auto max-w-lg p-4",children:e.jsxs("div",{className:"rounded-xl bg-white",children:[e.jsxs("div",{className:"flex items-start gap-3 p-4",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-1 text-xl font-medium",children:"Membership plan has been updated."}),e.jsxs("p",{className:"text-sm text-gray-600",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name,"'s plan has been changed to"," ",d==null?void 0:d.plan_name,"."]})]})]}),e.jsx("div",{className:"mt-4 flex justify-end border-t border-gray-200 p-4",children:e.jsx("button",{onClick:m,className:"rounded-lg bg-green-800 px-4 py-2 text-sm text-white",children:"Close"})})]})})),fs=({currentPlan:j,newPlan:d,onBack:m,onConfirm:t,user:n})=>{const[p,v]=o.useState(!1);return console.log(d,"new plan"),e.jsxs("div",{className:"mx-auto max-w-lg rounded-xl bg-white  shadow-5",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold",children:"Change membership plan profile"}),e.jsxs("p",{className:"text-gray-600",children:["You are about to change ",n==null?void 0:n.first_name," ",n==null?void 0:n.last_name,"'s membership plan. ",n==null?void 0:n.first_name," ",n==null?void 0:n.last_name," will be notified about plan change."]}),e.jsxs("div",{className:"my-6 space-y-3",children:[e.jsxs("div",{children:["Current plan:"," ",e.jsxs("span",{className:"font-semibold",children:[j==null?void 0:j.name," (",ge(j==null?void 0:j.price),"/month)"]})]}),e.jsxs("div",{children:["New plan:"," ",e.jsxs("span",{className:"font-semibold",children:[d==null?void 0:d.name," (",ge(d==null?void 0:d.price),"/month)"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:p,onChange:E=>v(E.target.checked),className:"h-5 w-5 rounded border-gray-300"}),e.jsx("span",{children:"I understand and want to proceed."})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t border-gray-200 p-4",children:[e.jsx("button",{onClick:m,className:"rounded-lg border border-gray-200 px-6 py-2",children:"Go back"}),e.jsx("button",{onClick:t,disabled:!p,className:"rounded-lg bg-primaryBlue px-6 py-2 text-white disabled:bg-gray-300",children:"Update membership"})]})]})},js=({currentPlan:j,newPlan:d,onBack:m,onConfirm:t,user:n})=>{const[p,v]=o.useState(!1);return e.jsxs("div",{className:"mx-auto mt-5 max-w-lg rounded-2xl  bg-white shadow-5",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold",children:"Cancel membership plan profile"}),e.jsxs("p",{className:"text-gray-600",children:["You are about to cancel ",n==null?void 0:n.first_name," ",n==null?void 0:n.last_name,"'s membership plan."]}),e.jsxs("div",{className:"my-6 space-y-3",children:[e.jsxs("div",{children:["Current plan:"," ",e.jsx("span",{className:"font-semibold",children:"Free (0.00$/month)"})]}),e.jsxs("div",{children:["New plan plan:"," ",e.jsx("span",{className:"font-semibold",children:"Member (9.99$/month)"})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:p,onChange:E=>v(E.target.checked),className:"h-5 w-5 rounded border-gray-300"}),e.jsx("span",{children:"I understand and want to proceed."})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t border-gray-200 p-4",children:[e.jsx("button",{onClick:m,className:"rounded-lg border border-gray-200 px-6 py-2",children:"Go back"}),e.jsx("button",{onClick:t,disabled:!p,className:"rounded-lg bg-red-600 px-6 py-2 text-white disabled:opacity-50",children:"Cancel membership"})]})]})},ys=({onClose:j})=>e.jsx("div",{className:"mx-auto max-w-lg p-4",children:e.jsxs("div",{className:"rounded-xl bg-white",children:[e.jsxs("div",{className:"flex items-start gap-3 p-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"mb-1 text-xl font-medium",children:"Membership plan has been canceled."})})]}),e.jsx("div",{className:"mt-4 flex justify-end border-t border-gray-200 p-4",children:e.jsx("button",{onClick:j,className:"rounded-lg bg-green-800 px-4 py-2 text-sm text-white",children:"Close"})})]})}),bs=({isOpen:j,onClose:d,currentPlan:m,tab:t,user:n,membershipPlans:p})=>{const[v,E]=o.useState(t||"edit"),[D,w]=o.useState(null),[y,h]=o.useState(!1),[b,f]=o.useState(!1),q=S=>{w(S)},Z=()=>{h(!0)},H=()=>{w(null)},B=()=>{f(!0)},O=()=>{h(!1),f(!1),w(null),d()};return console.log("membershipPlans",p),e.jsx(ts,{isOpen:j,onClose:d,title:y||b?"":`${n==null?void 0:n.first_name} ${n==null?void 0:n.last_name} • Edit membership`,children:b?e.jsx(ys,{onClose:O}):y?e.jsx(gs,{oldPlan:m,newPlan:D,onClose:O,user:n}):D?e.jsx(fs,{currentPlan:"Michale Smith",newPlan:D,onBack:H,onConfirm:Z,user:n}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mx-auto flex max-w-4xl items-center justify-center",children:[e.jsx("button",{className:`w-32 rounded-l-xl border px-2 py-1 ${v==="edit"?"bg-white":""}`,onClick:()=>E("edit"),children:"Edit"}),e.jsx("button",{className:`w-32 rounded-r-xl border px-2 py-1 ${v==="cancel"?"bg-white":""}`,onClick:()=>E("cancel"),children:"Cancel"})]}),v==="edit"&&e.jsx("div",{className:"mt-4 grid grid-cols-1 gap-4 md:grid-cols-3",children:(p==null?void 0:p.length)>0&&(p==null?void 0:p.map(S=>e.jsx(as,{...S,isCurrentPlan:m===S.id,onSelect:()=>q(S)},S.plan_name)))}),v==="cancel"&&e.jsx(js,{user:n,onBack:H,onConfirm:B})]})})};let J=new ye;const Me=[{header:"Date",accessor:"date"},{header:"Sport",accessor:"sport"},{header:"Type",accessor:"type"},{header:"Event",accessor:"event"},{header:"Players",accessor:"players"},{header:"Bill",accessor:"bill"},{header:"Status",accessor:"status"},{header:"",accessor:"actions"}],Ns=({user:j})=>{const{dispatch:d}=l.useContext(fe),{dispatch:m}=l.useContext(Xe),[t,n]=l.useState([]),[p,v]=l.useState(10),[E,D]=l.useState(0),[w,y]=l.useState(0),[h,b]=l.useState(0),[f,q]=l.useState(!1),[Z,H]=l.useState(!1),[B,O]=l.useState(!1);l.useState(!1),l.useState([]),l.useState([]),l.useState("eq");const[S,N]=l.useState(!0),[U,be]=l.useState(!1),[Q,Te]=l.useState(!1);l.useState(),Ae();const W=l.useRef(null),Ne=ls({id:ie(),email:ie(),role:ie(),status:ie()});ns({resolver:rs(Ne)});function oe(){I(h-1,p)}function z(){I(h+1,p)}async function I(a,A,u={},x=[]){N(!(Q||U));try{J.setTable("booking");const g=await J.callRestAPI({payload:{...u},page:a,limit:A,filter:[...x,`player_id,eq,${j.id}`]},"PAGINATE");J.setTable("sports");const se=await J.callRestAPI({},"GETALL"),te=g.list.map(L=>{const $=se.list.find(me=>me.id===L.sport_id);return{...L,sport:($==null?void 0:$.name)||"--"}});g&&N(!1);const{total:ce,limit:K,num_pages:ae,page:le}=g;n(te),v(K),D(ae),b(le),y(ce),q(le>1),H(le+1<=ae)}catch(g){N(!1),console.log("ERROR",g),je(m,g.message)}}l.useEffect(()=>{d({type:"SETPATH",payload:{path:"users"}});const A=setTimeout(async()=>{await I(1,p)},700);return()=>{clearTimeout(A)}},[]);const ee=a=>{W.current&&!W.current.contains(a.target)&&O(!1)};l.useEffect(()=>(document.addEventListener("mousedown",ee),()=>{document.removeEventListener("mousedown",ee)}),[]);const de=async a=>{if(window.confirm("Are you sure you want to delete this clinic?"))try{J.setTable("find_a_buddy"),await J.callRestAPI({id:a},"DELETE"),I(h,p)}catch(A){console.error("Error deleting clinic:",A),je(m,A.message)}};return e.jsxs("div",{className:"h-screen w-full",children:[S?e.jsx(os,{}):e.jsxs("div",{className:"",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full table-auto border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsx("tr",{children:Me.map((a,A)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:a.header},A))})}),e.jsx("tbody",{className:" ",children:t.map((a,A)=>e.jsx("tr",{className:"hover:bg-gray-40 rounded-xl bg-gray-100 px-4 py-3 text-gray-500",children:Me.map((u,x)=>u.accessor==""?e.jsx("td",{className:"whitespace-nowrap rounded-l-xl px-6 py-4",children:e.jsxs("div",{className:"flex items-center  gap-3",children:[e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"square","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20951L13.5768 2.67377C13.9022 2.34833 14.4298 2.34833 14.7553 2.67376L17.3268 5.24525C17.6522 5.57069 17.6522 6.09833 17.3268 6.42377L14.791 8.95951M11.041 5.20951L2.53509 13.7154C2.37881 13.8717 2.29102 14.0837 2.29102 14.3047V17.7095H5.69584C5.91685 17.7095 6.12881 17.6217 6.28509 17.4654L14.791 8.95951M11.041 5.20951L14.791 8.95951",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{onClick:()=>de(a.id),className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},x):u.mapping&&u.accessor==="status"?e.jsx("td",{className:"inline-block whitespace-nowrap px-6 py-5 text-sm",children:a[u.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:u.mapping[a[u.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:u.mapping[a[u.accessor]]})},x):u.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:u.mapping[a[u.accessor]]},x):u.accessor==="date"?e.jsxs("td",{className:"whitespace-nowrap rounded-l-xl px-6 py-4",children:[Qe(a==null?void 0:a.date)," "," | "," ",ke(a==null?void 0:a.start_time)," "," - "," ",ke(a==null?void 0:a.end_time)]},x):u.accessor==="players"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a!=null&&a.player_ids?`${JSON.parse(a==null?void 0:a.player_ids).length} players`:"0 players"},x):u.accessor==="type"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.type===1&&" Indoor"||a.type===2&&" Outdoor"},x):u.accessor==="bill"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:ge(a==null?void 0:a.price)},x):u.accessor==="status"?e.jsxs("td",{className:"whitespace-nowrap rounded-r-xl px-6 py-4",children:[a.status==1&&e.jsxs("div",{className:"flex items-center justify-center gap-1 rounded-lg border px-2 py-1 text-sm",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.00065 1.33398C4.31875 1.33398 1.33398 4.31875 1.33398 8.00065C1.33398 11.6825 4.31875 14.6673 8.00065 14.6673C11.6825 14.6673 14.6673 11.6825 14.6673 8.00065C14.6673 4.31875 11.6825 1.33398 8.00065 1.33398ZM10.3876 6.6506C10.5625 6.43688 10.531 6.12187 10.3173 5.94701C10.1035 5.77214 9.78854 5.80364 9.61367 6.01737L6.96353 9.25643L6.02087 8.31376C5.82561 8.1185 5.50903 8.1185 5.31376 8.31376C5.1185 8.50903 5.1185 8.82561 5.31376 9.02087L6.6471 10.3542C6.74699 10.4541 6.88447 10.5071 7.02556 10.5C7.16665 10.493 7.29818 10.4266 7.38763 10.3173L10.3876 6.6506Z",fill:"#2D9F75"})})}),e.jsx("span",{children:"Paid"})]}),a.status==0&&e.jsxs("div",{className:"flex items-center justify-center gap-1 rounded-lg border px-2 py-1 text-sm",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5 4H4V3H12V4H11V5C11 5.80771 10.5919 6.45734 10.078 6.98834C9.7266 7.3515 9.299 7.686 8.86545 8C9.299 8.314 9.7266 8.6485 10.078 9.01165C10.5919 9.54265 11 10.1923 11 11V12H12V13H4V12H5V11C5 10.1923 5.40808 9.54265 5.92195 9.01165C6.27341 8.6485 6.70101 8.314 7.13455 8C6.70101 7.686 6.27341 7.3515 5.92195 6.98834C5.40808 6.45734 5 5.80771 5 5V4ZM6 4V5C6 5.34257 6.13013 5.6675 6.38565 6H9.61435C9.86985 5.6675 10 5.34257 10 5V4H6ZM8 8.61095C7.4774 8.9801 7.004 9.3315 6.64055 9.7071C6.54504 9.8058 6.46003 9.9032 6.38565 10H9.61435C9.53995 9.9032 9.45495 9.8058 9.35945 9.7071C8.996 9.3315 8.5226 8.9801 8 8.61095Z",fill:"#162664"})})}),e.jsx("span",{children:"Reserved"})]})]},x):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a[u.accessor]},x))},A))})]})}),S&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!S&&t.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(is,{currentPage:h,pageCount:E,pageSize:p,canPreviousPage:f,canNextPage:Z,updatePageSize:a=>{v(a),I(1,a)},previousPage:oe,nextPage:z,gotoPage:a=>I(a,p)})]})};let c=new ye;const Cs=({sports:j,club:d})=>{const{dispatch:m}=l.useContext(fe),[t,n]=l.useState({}),[p,v]=l.useState(!0),[E,D]=l.useState("personal"),w=Ke(),[y,h]=l.useState(null),[b,f]=l.useState("");l.useState([]),l.useState(null),l.useState(null);const[q,Z]=l.useState(!1),[H,B]=l.useState(null),[O,S]=l.useState(!1),[N,U]=l.useState(!1),[be,Q]=o.useState(!1),[Te,W]=o.useState(null),[Ne,oe]=o.useState([]),[z,I]=o.useState([]),ee=Ae(),[de,a]=o.useState(!1),[A,u]=o.useState(!1),[x,g]=o.useState(null);o.useState(!1);const[se,te]=o.useState(!1),[ce,K]=o.useState(!1),[ae,le]=o.useState(null),[L,$]=o.useState(null);o.useState([]);const[me,Ce]=o.useState(!1),[_e,ve]=o.useState([]),we=localStorage.getItem("role"),[T,Fe]=o.useState(null);o.useState(null);const[ws,Ve]=o.useState(!1),[De,Se]=o.useState(!1),[Ie,xe]=o.useState(!1),[Re,pe]=o.useState(!1),[Pe,Ee]=o.useState(!1),[Ge,Le]=o.useState(!1);async function R(){var s,i,r,F;try{v(!0),c.setTable("profile");const C=await c.callRestAPI({id:Number(w==null?void 0:w.id),join:"user_id|user"},"GET");console.log("profile result",C),c.setTable("user");const he=await c.callRestAPI({},"GETALL");c.setTable("user");const V=await c.callRestAPI({filter:[`id,eq,${(s=C==null?void 0:C.model)==null?void 0:s.user_id}`]},"GETALL");$(C==null?void 0:C.model),n((i=V==null?void 0:V.list)==null?void 0:i[0]),c.setTable("user_groups");const ne=await c.callRestAPI({filter:[`user_id,eq,${(r=C==null?void 0:C.model)==null?void 0:r.user_id}`]},"GETALL");c.setTable("clubs");const X=await c.callRestAPI({id:(F=V==null?void 0:V.list)==null?void 0:F[0].club_id},"GET");Fe(X==null?void 0:X.model);const _=ne.list.map(Y=>({...Y,members:JSON.parse(Y.members).map(G=>he.list.find(ze=>parseInt(ze.id)==parseInt(G)))||[]}));I(_),C.error||v(!1)}catch(C){v(!1),console.log("error",C),je(m,C.message)}}const He=async()=>{try{c.setTable("stripe_price");const s=await c.callRestAPI({},"GETALL"),i=await c.getCustomerStripeSubscription();W(i==null?void 0:i.customer),oe(s.list)}catch(s){console.log(s)}};l.useEffect(function(){R(),He(),m({type:"SETPATH",payload:{path:"users"}})},[d==null?void 0:d.id]),console.log(t);const Ze=({member:s})=>{const[i,r]=l.useState(null),[F,C]=l.useState(""),[he,V]=l.useState(!1),{dispatch:ne}=l.useContext(fe),X=async(_,Y)=>{try{V(!0);const G=new ye;G.setTable("users"),(await G.callRestAPI({id:s.id,[_]:Y},"PUT")).error||(M(ne,"Updated successfully",3e3,"success"),r(null),R())}catch(G){M(ne,G==null?void 0:G.message,3e3,"error")}finally{V(!1)}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm font-medium uppercase text-gray-500",children:"MEMBER"}),e.jsxs("div",{className:"mt-2 flex items-center gap-3",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:"",className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("span",{className:"text-lg",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-4 text-sm font-medium uppercase text-gray-500",children:"DETAILS"}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"FIRST NAME"},{key:"last_name",label:"LAST NAME"},{key:"gender",label:"GENDER"},{key:"family_role",label:"FAMILY ROLE"},{key:"email",label:"EMAIL"},{key:"phone_number",label:"PHONE NUMBER"},{key:"date_of_birth",label:"DATE OF BIRTH"},{key:"address",label:"ADDRESS"},{key:"city",label:"CITY"},{key:"state",label:"STATE"},{key:"zip_code",label:"ZIP CODE"},{key:"alternate_phone_number",label:"ALTERNATE PHONE NUMBER"},{key:"age_group",label:"AGE GROUP"},{key:"bio",label:"ADDITIONAL INFO"}].map(_=>e.jsx("div",{children:i===_.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:_.label}),e.jsx("button",{onClick:()=>r(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"text",value:F,onChange:Y=>C(Y.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(k,{loading:he,onClick:()=>X(_.key,F),className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:_.label}),e.jsx("button",{onClick:()=>{r(_.key),C((s==null?void 0:s[_.key])||"")},className:"text-sm text-primaryBlue",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(s==null?void 0:s[_.key])||"--"})]})},_.key))})]})]})})},P=async(s,i)=>{console.log(s,i);try{U(!0),s==="gender"?(c.setTable("profile"),(await c.callRestAPI({id:L==null?void 0:L.id,[s]:i},"PUT")).error||(M(m,"Profile updated successfully",3e3,"success"),$({...L,[s]:i}),h(null),f(""))):(c.setTable("user"),(await c.callRestAPI({id:Number(w==null?void 0:w.id),[s]:i},"PUT")).error||(M(m,"Profile updated successfully",3e3,"success"),n({...t,[s]:i}),h(null),f("")))}catch(r){M(m,r==null?void 0:r.message,3e3,"error"),console.log(r)}finally{U(!1)}},Be=async s=>{try{U(!0);let i=new FormData;i.append("file",s);let r=await c.uploadImage(i);P("photo",r==null?void 0:r.url)}catch(i){M(m,i==null?void 0:i.message,3e3,"error"),console.log(i)}finally{U(!1)}},Oe=()=>{P("photo",null),n({...t,photo:null})},Ue=async()=>{u(!0);try{c.setTable("user_groups"),(await c.callRestAPI({id:x.id},"DELETE")).error||(M(m,"Group deleted successfully",3e3,"success"),R())}catch(s){M(m,s==null?void 0:s.message,3e3,"error")}finally{u(!1),a(!1),g(null)}},$e=async()=>{te(!0);try{const s={id:x.id,members:JSON.stringify(x.members.filter(r=>r.id!==H.id).map(r=>Number(r.id)))};c.setTable("user_groups"),(await c.callRestAPI(s,"PUT")).error||(M(m,"Member removed from group successfully",3e3,"success"),R())}catch(s){M(m,s==null?void 0:s.message,3e3,"error")}finally{te(!1),K(!1),g(null),B(null)}},Ye=async()=>{var s,i;Ce(!0);try{let r;we=="admin"?r=await c.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${T==null?void 0:T.user_id}`,{},"GET"):r=await c.callRawAPI(`/v3/api/custom/courtmatchup/club/profile/${T==null?void 0:T.user_id}`,{},"GET"),console.log("view model response",r),ve(JSON.parse((i=(s=r==null?void 0:r.model)==null?void 0:s.club)==null?void 0:i.membership_settings)||[])}catch(r){console.log(r)}finally{Ce(!1)}};o.useEffect(()=>{we=="admin"?T!=null&&T.user_id&&Ye():ve(d!=null&&d.membership_settings?JSON.parse(d.membership_settings):[])},[T==null?void 0:T.user_id]);const Je=s=>{g(s),Ve(!0)};return e.jsxs(We,{isLoading:N||se||me,children:[e.jsxs("div",{className:"w-full  p-4",children:[p?e.jsx(es,{}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsxs("h1",{className:"text-2xl font-medium",children:[t==null?void 0:t.first_name,"'s profile"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:"Current plan: None"}),e.jsx("button",{className:"text-primaryBlue underline",onClick:()=>Q(!0),children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:underline",onClick:()=>S(!0),children:"Delete"})]})]}),e.jsxs("div",{className:"mb-6 flex gap-4 border-b",children:[e.jsx("button",{className:`px-1 pb-2 ${E==="personal"?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-600"}`,onClick:()=>D("personal"),children:"Personal information"}),e.jsx("button",{className:`px-1 pb-2 ${E==="reservations"?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-600"}`,onClick:()=>D("reservations"),children:"Reservations"})]}),E==="personal"&&e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-end gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1",children:"Upload Image"}),e.jsx("p",{className:"my-2 text-sm text-gray-600",children:"Min 400x400px, PNG or JPEG"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{disabled:!(t!=null&&t.photo),className:"rounded-lg border border-red-600 px-2 py-1 text-red-600 disabled:opacity-50",onClick:Oe,children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-lg border border-gray-300 px-2 py-1 text-gray-500",children:[e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png",className:"hidden",onChange:s=>{const i=s.target.files[0];i&&Be(i)}}),"Change Photo"]})]})]})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:y==="first_name"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"First Name"}),e.jsx("button",{onClick:()=>h(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"text",value:b,onChange:s=>f(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(k,{loading:N,onClick:()=>{P(y,b)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"First name"}),e.jsx("div",{children:t==null?void 0:t.first_name})]}),e.jsx(k,{loading:N,onClick:()=>{h("first_name"),f((t==null?void 0:t.first_name)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:y==="last_name"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Last Name"}),e.jsx("button",{onClick:()=>h(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"text",value:b,onChange:s=>f(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(k,{loading:N,onClick:()=>{P(y,b)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Last name"}),e.jsx("div",{children:t==null?void 0:t.last_name})]}),e.jsx(k,{loading:N,onClick:()=>{h("last_name"),f((t==null?void 0:t.last_name)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:y==="gender"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Gender"}),e.jsx("button",{onClick:()=>h(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsxs("select",{value:b,onChange:s=>f(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2",children:[e.jsx("option",{disabled:!0,value:"",children:"Select Gender"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"})]}),e.jsx(k,{loading:N,onClick:()=>{P(y,b)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between  py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Gender"}),e.jsx("div",{children:(L==null?void 0:L.gender)||"(gender)"})]}),e.jsx(k,{loading:N,onClick:()=>{h("gender"),f((L==null?void 0:L.gender)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:y==="email"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Email"}),e.jsx("button",{onClick:()=>h(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"email",value:b,onChange:s=>f(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(k,{loading:N,onClick:()=>{P(y,b)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Your email is not shared with other users."})]}):e.jsxs("div",{className:"flex items-start justify-between border-t py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Email"}),e.jsx("div",{children:(t==null?void 0:t.email)||"<EMAIL>"}),e.jsxs("p",{className:"flex items-center gap-1 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),"Your email is not shared with other users."]})]}),e.jsx(k,{loading:N,onClick:()=>{h("email"),f((t==null?void 0:t.email)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:y==="phone"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Phone Number"}),e.jsx("button",{onClick:()=>h(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"tel",value:b,onChange:s=>f(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(k,{loading:N,onClick:()=>{P(y,b)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Your phone number is not shared with other users."})]}):e.jsxs("div",{className:"flex items-start justify-between border-t py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Phone number"}),e.jsx("div",{children:e.jsx(us,{phoneNumber:t==null?void 0:t.phone,format:"US"})}),e.jsxs("p",{className:"flex items-center gap-1 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),"Your phone number is not shared with other users."]})]}),e.jsx(k,{loading:N,onClick:()=>{h("phone"),f((t==null?void 0:t.phone)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:y==="bio"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Bio"}),e.jsx("button",{onClick:()=>h(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("textarea",{value:b,onChange:s=>f(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2",rows:"4"}),e.jsx(k,{loading:N,onClick:()=>{P(y,b)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-start justify-between border-t py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Bio"}),e.jsx("div",{className:"max-w-lg",children:(t==null?void 0:t.bio)||"--"})]}),e.jsx(k,{loading:N,onClick:()=>{h("bio"),f((t==null?void 0:t.bio)||"")},className:"text-primaryBlue underline",children:"Edit"})]})})]})]}),e.jsx("div",{className:"h-fit space-y-4 rounded-lg bg-white p-5",children:e.jsx("div",{className:"grid grid-cols-1 gap-4",children:z.length>0?z.map(s=>e.jsx(ds,{group:s,onAddExistingUser:()=>{pe(!0),g(s)},onAddMember:Je,onEditName:()=>{Ee(!0),g(s)},onDeleteGroup:()=>{a(!0),g(s)},onViewProfile:i=>{const r=z.flatMap(F=>F.members).find(F=>F.id===i);r&&(B(r),Z(!0))},onRemoveMember:(i,r)=>{K(!0),B(r),g(i)},onAddFamilyMember:()=>{xe(!0),g(s)},onInviteToCourtmatch:()=>{Le(!0),g(s)}},s.id)):e.jsx("div",{className:"text-center text-gray-600",children:"No groups found"})})})]}),E==="reservations"&&e.jsx(Ns,{user:t})]}),e.jsx(re,{isOpen:q,onClose:()=>Z(!1),title:"Profile details",showFooter:!1,children:e.jsx(Ze,{member:H})}),e.jsx(ue,{isOpen:O,onClose:()=>S(!1),onDelete:async()=>{try{c.setTable("user"),(await c.callRestAPI({id:Number(w==null?void 0:w.id)},"DELETE")).error||(M(m,"Profile deleted successfully",3e3,"success"),S(!1),M(m,"Profile deleted successfully",3e3,"success"),ee("/club/users"))}catch(s){M(m,s==null?void 0:s.message,3e3,"error"),console.log(s)}},title:"Delete profile",message:"Are you sure you want to delete this profile?"}),e.jsx(bs,{isOpen:be,onClose:()=>Q(!1),currentPlan:ae,user:t,membershipPlans:_e})]}),e.jsx(ue,{isOpen:de,onClose:()=>a(!1),onDelete:Ue,loading:A,message:"Are you sure you want to delete this group?",title:"Delete group"}),e.jsx(ue,{isOpen:ce,onClose:()=>K(!1),onDelete:$e,loading:se,message:"Are you sure you want to remove this member from the group?",title:"Remove from group"}),e.jsx(re,{isOpen:Ie,onClose:()=>xe(!1),title:"Add family member",showFooter:!1,children:e.jsx(cs,{users:[],user:t,fetchData:R,group:x,onClose:()=>{xe(!1),g(null)}})}),e.jsx(re,{isOpen:De,onClose:()=>Se(!1),title:"Create group",showFooter:!1,children:e.jsx(ms,{users:[],fetchData:R,onClose:()=>Se(!1)})}),e.jsx(re,{isOpen:Re,onClose:()=>{pe(!1),g(null)},title:`Add user to ${x==null?void 0:x.name}`,showFooter:!1,children:e.jsx(xs,{users:[],group:x,fetchData:R,onClose:()=>{pe(!1),g(null)}})}),Pe&&e.jsx(ps,{title:`Edit ${x==null?void 0:x.name}`,group:x,onClose:s=>{Ee(!1),g(null),s&&R()}}),Ge&&e.jsx(hs,{title:"Invite a friend to Court Matchup",onClose:()=>{Le(!1),g(null)},group:x})]})},vs=Cs;function yt(){const{club:j,sports:d}=ss();return e.jsx(vs,{club:j,sports:d})}export{yt as default};
