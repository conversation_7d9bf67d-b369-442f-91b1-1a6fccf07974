import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as Q,k as ue,f as xe}from"./vendor-851db8c1.js";import{M as ie,G as ce,b as I,e as ne,T as ye,A as he,d as pe,t as de,u as fe,f as ge,h as be,i as je,R as _e,F as ve,j as Ne,k as we,l as Ce}from"./index-3b44b02c.js";import{B as Se}from"./BackButton-11ba52b2.js";import{S as Ae}from"./StripeConnectionStatus-b2fa66dc.js";import{u as Te}from"./react-hook-form-687afde5.js";import{o as ke}from"./yup-2824f222.js";import{c as Ee,a as Ie}from"./yup-54691517.js";import"./index-02625b16.js";import{I as Pe}from"./ImageCropModal-44851a76.js";import{L as Le}from"./index.esm-3a36c7d6.js";import{b as le}from"./index.esm-9c6194ba.js";import{M as oe}from"./react-tooltip-7a26650a.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-image-crop-1f5038af.js";const $e=new ie;function Re(){const[h,P]=r.useState([{account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"}]);r.useState(!1),r.useState(null),r.useState({}),r.useState(!1),r.useState({account_nickname:"",account_number:"",account_routing_number:"",account_type:"savings"});const{dispatch:m}=Q.useContext(ce),[R,g]=r.useState(!0),F=Q.useRef(null),u=i=>{console.log("Stripe connection status changed:",i)};r.useEffect(()=>{_()},[]);const _=async()=>{g(!0);try{const i=await $e.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET"),C=JSON.parse(i.account_details||"[]");C.length>0&&Array.isArray(C)&&P(C)}catch(i){I(m,i.message,3e3,"error")}finally{g(!1)}};return e.jsxs("div",{className:"mx-auto flex w-full flex-col bg-white px-4 pb-7",children:[R&&e.jsx(ne,{}),e.jsxs("div",{className:"mx-auto flex w-full max-w-2xl flex-col justify-center",children:[e.jsx(Ae,{ref:F,onConnectionStatusChange:u,successMessage:"You can now pay staffs and coaches from your club.",noConnectionMessage:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),e.jsx("div",{className:"flex w-full flex-col self-center",children:e.jsx("p",{className:"mt-6 text-xs leading-4 text-neutral-400",children:"Note: Multiple accounts can be set up if you want to change accounts later on a particular month or year."})})]})]})}let ae=new ie,Fe=new ye;const Me=()=>{const h=Ee({email:Ie().email().required()}).required(),{dispatch:P}=Q.useContext(he),[m,R]=r.useState("");Q.useState({});const[g,F]=r.useState("");r.useState(!1);const[u,_]=r.useState(!1),[i,C]=r.useState({}),[S,M]=r.useState(!0),[se,A]=r.useState(null),[B,U]=r.useState(""),{dispatch:T}=Q.useContext(ce),[re,H]=r.useState(!1),[f,J]=r.useState(null),[b,Z]=r.useState(!1),[K,Y]=r.useState(null),{register:v,handleSubmit:V,setError:s,setValue:p,formState:{errors:N}}=Te({resolver:ke(h)}),L=localStorage.getItem("user");async function w(){var n;M(!0);try{const o=await Fe.getList("profile",{filter:[`user_id,eq,${L}`],join:["user|user_id"]}),l=(n=o==null?void 0:o.list)==null?void 0:n[0];if(l){const t=l.user||{},a=l.id,c={...l,...t,profile_id:a,user_id:t.id};C(c),p("email",t==null?void 0:t.email),p("first_name",t==null?void 0:t.first_name),p("last_name",t==null?void 0:t.last_name),p("phone",t==null?void 0:t.phone),p("bio",t==null?void 0:t.bio),R(t==null?void 0:t.email),F(t==null?void 0:t.photo),p("gender",l==null?void 0:l.gender),p("address",l==null?void 0:l.address),p("city",l==null?void 0:l.city),p("state",l==null?void 0:l.state),p("zip_code",l==null?void 0:l.zip_code),P({type:"UPDATE_PROFILE",payload:c}),M(!1)}}catch(o){de(P,o.response.data.message?o.response.data.message:o.message)}}const X=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],ee=["gender","address","city","state","zip_code","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:i==null?void 0:i.user_id,profile_id:i==null?void 0:i.profile_id,defaultValues:i});const W=async(n,o)=>{try{_(!0);const l={[n]:o},t=X.includes(n),a=ee.includes(n);if(t){ae.setTable("user");const c=await ae.callRestAPI({id:i==null?void 0:i.user_id,...l},"PUT");c.error?$(c):(I(T,"Profile Updated",4e3),A(null),U(""),w())}else if(a){ae.setTable("profile");const c=await ae.callRestAPI({id:i==null?void 0:i.profile_id,...l},"PUT");c.error?$(c):(I(T,"Profile Updated",4e3),A(null),U(""),w())}else{I(T,"Unknown field type: "+n,4e3,"error"),_(!1);return}_(!1)}catch(l){_(!1),s(n,{type:"manual",message:l!=null&&l.message&&l==null?void 0:l.message}),de(P,l!=null&&l.message&&l==null?void 0:l.message)}},$=n=>{if(n.validation){const o=Object.keys(n.validation);for(let l=0;l<o.length;l++){const t=o[l];s(t,{type:"manual",message:n.validation[t]})}}},k=n=>{try{if(n.size>2*1024*1024){I(T,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}Y(n.type);const o=new FileReader;o.onload=()=>{J(o.result),Z(!0)},o.readAsDataURL(n)}catch(o){I(T,o==null?void 0:o.message,3e3,"error"),console.log(o)}},G=async n=>{try{H(!0);const o=K==="image/png",l=new File([n],`cropped_profile.${o?"png":"jpg"}`,{type:o?"image/png":"image/jpeg"});let t=new FormData;t.append("file",l);let a=await ae.uploadImage(t);W("photo",a==null?void 0:a.url)}catch(o){I(T,o==null?void 0:o.message,3e3,"error"),console.log(o)}finally{H(!1)}},q=()=>{W("photo",null),C({...i,photo:null})};return Q.useEffect(()=>{w()},[]),e.jsxs("div",{className:"",children:[S||re&&e.jsx(ne,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(Pe,{isOpen:b,onClose:()=>Z(!1),image:f,onCropComplete:G}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:g||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:q,disabled:!g,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:n=>k(n.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"bio",label:"Bio",type:"textarea"}].map(n=>e.jsx("div",{children:se===n.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:n.label}),e.jsx("button",{onClick:()=>A(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),n.type==="select"?e.jsxs("select",{value:B,onChange:o=>U(o.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",n.label.toLowerCase()]}),n.options.map(o=>e.jsx("option",{value:o,children:o.charAt(0).toUpperCase()+o.slice(1)},o))]}):n.type==="textarea"?e.jsx("textarea",{value:B,onChange:o=>U(o.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:B,onChange:o=>U(o.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),n.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:n.note}),e.jsx("div",{className:"mt-2",children:e.jsx(pe,{loading:u,onClick:()=>W(n.key,B),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:n.label}),e.jsx("button",{onClick:()=>{A(n.key),U((i==null?void 0:i[n.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(i==null?void 0:i[n.key])||"--"}),n.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:n.note})]})},n.key))})]})]})})]})};let Be=new ie;const Ue=()=>{const[h,P]=r.useState({}),[m,R]=r.useState(""),[g,F]=r.useState(""),[u,_]=r.useState(""),[i,C]=r.useState("date"),[S,M]=r.useState("desc"),[se,A]=r.useState([]),[B,U]=r.useState(!1),[T,re]=r.useState(null),[H,f]=r.useState("all");fe();const J=s=>{P(p=>({...p,[s]:!p[s]}))},b=async()=>{U(!0);try{const s=await Be.callRawAPI("/v3/api/custom/courtmatchup/coach/bookings/billing/coach-invoices",{},"GET");re(s),A(s.invoices||[])}catch(s){console.log(s)}finally{U(!1)}},K=[...se.filter(s=>{var w,X,ee,W,$;const p=((w=s.user_first_name)==null?void 0:w.toLowerCase().includes(m.toLowerCase()))||((X=s.user_last_name)==null?void 0:X.toLowerCase().includes(m.toLowerCase()))||((ee=s.receipt_id)==null?void 0:ee.toLowerCase().includes(m.toLowerCase()))||((W=s.status)==null?void 0:W.toLowerCase().includes(m.toLowerCase())),N=H==="all"||(($=s.invoice_type)==null?void 0:$.toLowerCase())===H.toLowerCase(),L=(()=>{if(!g&&!u)return!0;if(!s.date)return!1;const k=new Date(s.date),G=g?new Date(g):null,q=u?new Date(u):null;return G&&q?k>=G&&k<=q:G?k>=G:q?k<=q:!0})();return p&&N&&L})].sort((s,p)=>{let N=0;if(i==="date"){const L=new Date(s.date||s.create_at),w=new Date(p.date||p.create_at);N=L.getTime()-w.getTime()}else if(i==="amount")N=(s.amount||0)-(p.amount||0);else if(i==="status")N=(s.status||"").localeCompare(p.status||"");else if(i==="customer"){const L=`${s.user_first_name||""} ${s.user_last_name||""}`.trim(),w=`${p.user_first_name||""} ${p.user_last_name||""}`.trim();N=L.localeCompare(w)}return S==="desc"?-N:N}),Y=()=>{i==="date"?(C("amount"),M("desc")):i==="amount"?(C("status"),M("asc")):i==="status"?(C("customer"),M("asc")):(C("date"),M(S==="desc"?"asc":"desc"))},v=(s,p="usd")=>new Intl.NumberFormat("en-US",{style:"currency",currency:p.toUpperCase()}).format(s),V=s=>s?new Date(s).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A";return r.useEffect(()=>{b()},[]),B?e.jsx("div",{className:"flex w-full items-center justify-center py-8",children:e.jsx("div",{className:"text-lg",children:"Loading invoices..."})}):e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"My invoices"}),T&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Total: ",T.total_invoices||0," invoices • Total Earnings: ",v(T.total_earnings||0)]})]})}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(ge,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:m,onChange:s=>R(s.target.value),placeholder:"Search invoices...",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:g,onChange:s=>F(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:u,onChange:s=>_(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(g||u)&&e.jsx("button",{onClick:()=>{F(""),_("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:Y,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:["By ",i," ",S==="desc"?"↓":"↑",e.jsx(Le,{className:"transform"})]})]}),e.jsx("div",{className:"space-y-4",children:K.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No invoices found."}):K.map(s=>{var p;return e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>J(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(be,{className:`transform transition-transform ${h[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.invoice_type||s.type}),s.status&&e.jsx("span",{className:`rounded-full px-2 py-1 text-xs ${s.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:s.status})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:V(s.date)}),e.jsx("span",{className:"font-medium",children:v(s.amount,s.currency)})]})]}),h[s.id]&&e.jsxs("div",{className:"mt-4 space-y-3 border-t border-gray-200 p-4",children:[s.receipt_id&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice Number"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),s.user_first_name&&s.user_last_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Customer"}),e.jsxs("span",{children:[s.user_first_name," ",s.user_last_name]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Created on"}),e.jsx("span",{children:V(s.create_at)})]}),s.total_amount&&s.total_amount!==s.amount&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total amount"}),e.jsx("span",{children:v(s.total_amount,s.currency)})]}),s.valid_until&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Period"}),e.jsxs("span",{children:[V(s.create_at)," -"," ",V(s.valid_until)]})]}),s.club_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Club"}),e.jsx("span",{children:s.club_name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),s.reservation_type&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Reservation type"}),e.jsx("span",{className:"capitalize",children:(p=je.find(N=>N.value===s.reservation_type))==null?void 0:p.label})]}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50",onClick:()=>{const N=`
                        <div style="padding: 20px; font-family: Arial, sans-serif;">
                          <h2>Invoice Receipt</h2>
                          <p><strong>Receipt ID:</strong> ${s.receipt_id||"N/A"}</p>
                          <p><strong>Amount:</strong> ${v(s.amount,s.currency)}</p>
                          <p><strong>Date:</strong> ${V(s.date)}</p>
                          <p><strong>Status:</strong> ${s.status}</p>
                          <p><strong>Customer:</strong> ${s.user_first_name} ${s.user_last_name}</p>
                          <p><strong>Payment Method:</strong> ${s.payment_method}</p>
                          ${s.club_name?`<p><strong>Club:</strong> ${s.club_name}</p>`:""}
                        </div>
                      `,L=window.open("","_blank");L.document.write(N),L.document.close(),L.print()},children:"Print receipt"})]})]},s.id)})})]})},E=new ie,Oe=()=>{var G,q,n,o,l;const[h,P]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[m,R]=r.useState({}),[g,F]=r.useState(!1),[u,_]=r.useState(!1),[i,C]=r.useState(!0),{dispatch:S}=r.useContext(ce),[M,se]=r.useState([]),[A,B]=r.useState(null),[U,T]=r.useState(!1),[re,H]=r.useState(null),[f,J]=r.useState({sport_id:"",type:"",sub_type:"",price:""}),[b,Z]=r.useState({}),[K,Y]=r.useState(!1),v=(M==null?void 0:M.filter(t=>t.status===1))||[];r.useEffect(()=>{(async()=>{C(!0);try{const a=await E.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(a);const c=await E.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${a.club_id}`,{},"GET");se(c.sports)}catch(a){console.error("Error fetching data:",a),I(S,a.message,3e3,"error")}finally{C(!1)}})()},[S]);const V=t=>{var D,z,x;const a={};t.sport_id||(a.sport_id="Sport is required");const c=v.find(d=>d.id===parseInt(t.sport_id)),y=(D=c==null?void 0:c.sport_types)==null?void 0:D.some(d=>d.type&&d.type.trim()!=="");y&&!t.type&&(a.type="Type is required");let O=!1;if(y&&t.type&&t.type!=="All"){const d=(z=c==null?void 0:c.sport_types)==null?void 0:z.find(j=>j.type===t.type);O=(x=d==null?void 0:d.subtype)==null?void 0:x.some(j=>j&&j.trim()!==""),O&&!t.sub_type&&(a.sub_type="Sub-type is required")}return t.price||(a.price="Price is required"),t.price&&Number(t.price)<=0&&(a.price="Price must be greater than 0"),t.type==="All"&&(delete a.type,t.sub_type==="All"&&delete a.sub_type),a},s=t=>{const{name:a,value:c}=t.target;P(y=>({...y,[a]:c})),R(y=>({...y,[a]:""}))},p=async()=>{var O,D,z;const t={...h},a=v.find(x=>x.id===parseInt(h.sport_id));if(!((O=a==null?void 0:a.sport_types)==null?void 0:O.some(x=>x.type&&x.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const x=(D=a==null?void 0:a.sport_types)==null?void 0:D.find(j=>j.type===t.type);((z=x==null?void 0:x.subtype)==null?void 0:z.some(j=>j&&j.trim()!==""))||(t.sub_type="")}const y=V(t);if(Object.keys(y).length>0){R(y);return}_(!0);try{E.setTable("coach_sports"),await E.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[t]},"POST"),P({sport_id:"",type:"",sub_type:"",price:""});const x=await E.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(x),I(S,"Sport added successfully",3e3,"success")}catch(x){console.log(x),I(S,x.message,3e3,"error")}finally{_(!1)}},N=async t=>{F(!0);try{E.setTable("coach_sports"),await E.callRestAPI({id:t},"DELETE");const a=await E.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(a),I(S,"Sport deleted successfully",3e3,"success")}catch(a){console.log(a),I(S,a.message,3e3,"error")}finally{F(!1)}},L=t=>{H(t),J({id:t.id,sport_id:t.sport_id,type:t.type||"",sub_type:t.sub_type||"",price:t.price||""}),Z({}),T(!0)},w=t=>{const{name:a,value:c}=t.target;J(y=>({...y,[a]:c})),Z(y=>({...y,[a]:""}))},X=t=>{var D,z,x;const a={};t.sport_id||(a.sport_id="Sport is required");const c=v.find(d=>d.id===parseInt(t.sport_id)),y=(D=c==null?void 0:c.sport_types)==null?void 0:D.some(d=>d.type&&d.type.trim()!=="");y&&!t.type&&(a.type="Type is required");let O=!1;if(y&&t.type&&t.type!=="All"){const d=(z=c==null?void 0:c.sport_types)==null?void 0:z.find(j=>j.type===t.type);O=(x=d==null?void 0:d.subtype)==null?void 0:x.some(j=>j&&j.trim()!==""),O&&!t.sub_type&&(a.sub_type="Sub-type is required")}return t.price||(a.price="Price is required"),t.price&&Number(t.price)<=0&&(a.price="Price must be greater than 0"),t.type==="All"&&(delete a.type,t.sub_type==="All"&&delete a.sub_type),a},ee=async()=>{var O,D,z,x;const t={...f},a=v.find(d=>d.id===parseInt(f.sport_id));if(!((O=a==null?void 0:a.sport_types)==null?void 0:O.some(d=>d.type&&d.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const d=(D=a==null?void 0:a.sport_types)==null?void 0:D.find(te=>te.type===t.type);((z=d==null?void 0:d.subtype)==null?void 0:z.some(te=>te&&te.trim()!==""))||(t.sub_type="")}const y=X(t);if(Object.keys(y).length>0){Z(y);return}Y(!0);try{const d=(x=A==null?void 0:A.sports)==null?void 0:x.find(me=>me.id===f.id);d&&parseInt(f.sport_id)!==parseInt(d.sport_id)?(E.setTable("coach_sports"),await E.callRestAPI({id:f.id},"DELETE"),await E.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[{sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,price:t.price}]},"POST")):(E.setTable("coach_sports"),await E.callRestAPI({id:f.id,sport_id:t.sport_id,type:t.type,sub_type:t.sub_type,price:t.price},"PUT"));const te=await E.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");B(te),T(!1),I(S,"Sport updated successfully",3e3,"success")}catch(d){console.log(d),I(S,d.message,3e3,"error")}finally{Y(!1)}},W=()=>{T(!1),H(null),J({sport_id:"",type:"",sub_type:"",price:""}),Z({})},$=v.find(t=>t.id===parseInt(h.sport_id)),k=v.find(t=>t.id===parseInt(f.sport_id));return i?e.jsx(ne,{}):e.jsxs("div",{className:"w-full",children:[g&&e.jsx(ne,{}),e.jsx("h1",{className:"mb-6 text-xl font-semibold text-gray-900",children:"Sports you offer"}),e.jsxs("div",{className:"space-y-6",children:[((G=A==null?void 0:A.sports)==null?void 0:G.length)>0?e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 shadow-sm",children:e.jsx("ul",{className:"divide-y divide-gray-200",children:A.sports.map((t,a)=>{var c;return e.jsxs("li",{className:"flex items-center justify-between py-3 transition-colors hover:bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(c=v.find(y=>y.id===parseInt(t.sport_id)))==null?void 0:c.name})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("span",{className:"font-medium",children:t.type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sub-type"}),e.jsx("span",{className:"font-medium",children:t.sub_type||"--"})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("span",{className:"font-medium",children:["$",t.price]})]})]}),e.jsxs("div",{className:"ml-4 flex gap-2",children:[e.jsx("button",{onClick:()=>L(t),className:"text-blue-500 hover:text-blue-700",title:"Edit sport",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.1667 2.5C14.3856 2.28113 14.6454 2.10752 14.9312 1.98906C15.2169 1.87061 15.5238 1.80957 15.8333 1.80957C16.1429 1.80957 16.4498 1.87061 16.7355 1.98906C17.0213 2.10752 17.2811 2.28113 17.5 2.5C17.7189 2.71887 17.8925 2.97869 18.0109 3.26445C18.1294 3.55021 18.1904 3.85714 18.1904 4.16667C18.1904 4.47619 18.1294 4.78312 18.0109 5.06888C17.8925 5.35464 17.7189 5.61446 17.5 5.83333L6.25 17.0833L1.66667 18.3333L2.91667 13.75L14.1667 2.5Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{onClick:()=>N(t.id),className:"text-red-500 hover:text-red-700",title:"Delete sport",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 15C8.55435 15 8.76631 14.9122 8.92259 14.7559C9.07887 14.5996 9.16667 14.3877 9.16667 14.1667V9.16667C9.16667 8.94565 9.07887 8.73369 8.92259 8.57741C8.76631 8.42113 8.55435 8.33333 8.33333 8.33333C8.11232 8.33333 7.90036 8.42113 7.74408 8.57741C7.5878 8.73369 7.5 8.94565 7.5 9.16667V14.1667C7.5 14.3877 7.5878 14.5996 7.74408 14.7559C7.90036 14.9122 8.11232 15 8.33333 15ZM16.6667 4.16667H13.3333V3.33333C13.3333 2.8913 13.1577 2.46738 12.8452 2.15482C12.5326 1.84226 12.1087 1.66667 11.6667 1.66667H8.33333C7.8913 1.66667 7.46738 1.84226 7.15482 2.15482C6.84226 2.46738 6.66667 2.8913 6.66667 3.33333V4.16667H3.33333C3.11232 4.16667 2.90036 4.25446 2.74408 4.41074C2.5878 4.56702 2.5 4.77899 2.5 5C2.5 5.22101 2.5878 5.43298 2.74408 5.58926C2.90036 5.74554 3.11232 5.83333 3.33333 5.83333H4.16667V16.6667C4.16667 17.1087 4.34226 17.5326 4.65482 17.8452C4.96738 18.1577 5.3913 18.3333 5.83333 18.3333H14.1667C14.6087 18.3333 15.0326 18.1577 15.3452 17.8452C15.6577 17.5326 15.8333 17.1087 15.8333 16.6667V5.83333H16.6667C16.8877 5.83333 17.0996 5.74554 17.2559 5.58926C17.4122 5.43298 17.5 5.22101 17.5 5C17.5 4.77899 17.4122 4.56702 17.2559 4.41074C17.0996 4.25446 16.8877 4.16667 16.6667 4.16667ZM8.33333 3.33333H11.6667V4.16667H8.33333V3.33333ZM14.1667 16.6667H5.83333V5.83333H14.1667V16.6667ZM11.6667 15C11.8877 15 12.0996 14.9122 12.2559 14.7559C12.4122 14.5996 12.5 14.3877 12.5 14.1667V9.16667C12.5 8.94565 12.4122 8.73369 12.2559 8.57741C12.0996 8.42113 11.8877 8.33333 11.6667 8.33333C11.4457 8.33333 11.2337 8.42113 11.0774 8.57741C10.9211 8.73369 10.8333 8.94565 10.8333 9.16667V14.1667C10.8333 14.3877 10.9211 14.5996 11.0774 14.7559C11.2337 14.9122 11.4457 15 11.6667 15Z",fill:"currentColor"})})})]})]},a)})})}):e.jsx("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 text-center shadow-sm",children:e.jsx("p",{className:"text-gray-500",children:"No sports added yet. Add your first sport below."})}),e.jsxs("div",{className:"rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Add New Sport"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("select",{name:"sport_id",value:h.sport_id,onChange:s,className:`w-full rounded-lg border ${m.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),v.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),m.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.sport_id})]}),$&&e.jsxs(e.Fragment,{children:[$.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type"}),e.jsx(le,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-type-tooltip",size:16}),e.jsx(oe,{id:"sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"type",value:h.type,onChange:s,className:`w-full rounded-lg border ${m.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Type"}),$.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),$.sport_types.map((t,a)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},a))]}),m.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.type})]}):null,h.type&&$.sport_types.some(t=>t.type===h.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(le,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-subtype-tooltip",size:16}),e.jsx(oe,{id:"sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:h.sub_type,onChange:s,className:`w-full rounded-lg border ${m.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),h.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((q=$.sport_types.find(t=>t.type===h.type))==null?void 0:q.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(n=$.sport_types.find(t=>t.type===h.type))==null?void 0:n.subtype.filter(t=>t&&t.trim()!=="").map((t,a)=>e.jsx("option",{value:t,children:t},a))]})]}),m.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.sub_type})]}),e.jsxs("div",{children:[e.jsx("input",{type:"number",name:"price",value:h.price,onChange:s,placeholder:"Enter price",className:`w-full rounded-lg border ${m.price?"border-red-500":"border-gray-300"} p-2`}),m.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:m.price})]}),e.jsx(pe,{type:"button",onClick:p,loading:u,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:u?"Adding Sport...":"Add Sport"})]})]})]})]}),e.jsx(_e,{isOpen:U,onClose:W,title:"Edit Sport",onPrimaryAction:ee,submitting:K,primaryButtonText:K?"Updating...":"Update Sport",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsxs("select",{name:"sport_id",value:f.sport_id,onChange:w,className:`w-full rounded-lg border ${b.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),v.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),b.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.sport_id})]}),k&&e.jsxs(e.Fragment,{children:[k.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type"}),e.jsx(le,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"edit-sport-type-tooltip",size:16}),e.jsx(oe,{id:"edit-sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"type",value:f.type,onChange:w,className:`w-full rounded-lg border ${b.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Type"}),k.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),k.sport_types.map((t,a)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},a))]}),b.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.type})]}):null,f.type&&k.sport_types.some(t=>t.type===f.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(le,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"edit-sport-subtype-tooltip",size:16}),e.jsx(oe,{id:"edit-sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:f.sub_type,onChange:w,className:`w-full rounded-lg border ${b.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),f.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[((o=k.sport_types.find(t=>t.type===f.type))==null?void 0:o.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(l=k.sport_types.find(t=>t.type===f.type))==null?void 0:l.subtype.filter(t=>t&&t.trim()!=="").map((t,a)=>e.jsx("option",{value:t,children:t},a))]})]}),b.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.sub_type})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Price ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"number",name:"price",value:f.price,onChange:w,placeholder:"Enter price",className:`w-full rounded-lg border ${b.price?"border-red-500":"border-gray-300"} p-2`}),b.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.price})]})]})]})})]})},It=()=>{const{dispatch:h}=Q.useContext(ce),[P]=ue(),[m,R]=r.useState("profile"),g=xe();r.useEffect(()=>{const u=P.get("tab");u&&R({"payment-methods":"payment-methods",profile:"profile",sports:"sports",membership:"membership",billing:"billing",invoices:"invoices"}[u]||"profile")},[P.get("tab")]);const F=[{label:"Profile details",value:"profile",icon:ve},{label:"Sports",value:"sports",icon:Ne},{label:"Bank Accounts",value:"payment-methods",icon:we},{label:"Invoices",value:"invoices",icon:Ce}];return r.useEffect(()=>{h({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(Se,{onBack:()=>g("/coach/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:F.map(u=>{const _=u.icon;return e.jsxs("button",{onClick:()=>{R(u.value),g(`/coach/profile?tab=${u.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${m===u.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(_,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:u.label})]},u.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[m==="profile"&&e.jsx(Me,{}),m==="sports"&&e.jsx(Oe,{}),m==="payment-methods"&&e.jsx(Re,{}),m==="invoices"&&e.jsx(Ue,{})]})]})]})})};export{It as default};
