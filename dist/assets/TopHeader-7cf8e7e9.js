import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as h,f as qe,u as $e,L as de}from"./vendor-851db8c1.js";import{M as he,A as re,t as ge,T as Ve,u as fe,G as ye,aS as Ge,ay as Ue,b6 as ue,b7 as me,b2 as Qe,aT as Ke,b8 as Q,b9 as pe,ba as Je,aR as xe}from"./index-3b44b02c.js";import{B as We}from"./BackButton-11ba52b2.js";import{NAV_ITEMS as Ye}from"./ClubHeader-bdd2d561.js";import{D as ze}from"./DownloadAppModal-a0338b02.js";import{u as Xe}from"./react-hook-form-687afde5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-qr-code-4a7125ac.js";function et({title:N,titleId:S,...g},f){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:f,"aria-labelledby":S},g),N?r.createElement("title",{id:S},N):null,r.createElement("path",{fillRule:"evenodd",d:"M12.53 16.28a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 0 1 1.06-1.06L12 14.69l6.97-6.97a.75.75 0 1 1 1.06 1.06l-7.5 7.5Z",clipRule:"evenodd"}))}const tt=r.forwardRef(et),st=tt,rt=(N=!1)=>{const S=new he,{state:{profile:g},dispatch:f}=h.useContext(re),[k,y]=h.useState(null),H=h.useCallback(()=>{(async()=>{try{const u=await S.getProfile();console.log(u),u!=null&&u.error||(y(()=>u),f({type:"UPDATE_PROFILE",payload:u}))}catch(u){console.log(u.message),ge(f,u.message)}})()},[k]);return h.useEffect(()=>{!g||N?H():y(g)},[g]),[k,y]};let d=new he,ot=new Ve;const at=()=>{const[N,S]=r.useState(!1),[g,f]=r.useState(null),[k,y]=r.useState(null),[H,u]=r.useState([]),[o,K]=r.useState([]),[ee,T]=r.useState([]),[J,A]=r.useState(!1),[B,_]=r.useState(""),[Z,a]=r.useState([]);r.useState(!1),r.useState(!1);const[w,I]=r.useState(!1),[P,W]=r.useState(!0),[F,nt]=r.useState(!0),[we,be]=r.useState(!1),[Y,D]=r.useState(!1),[M,O]=r.useState(!1);r.useState(null),r.useState(null);const[b,q]=r.useState(null),[oe,$]=r.useState(null),[je,lt]=r.useState([]);r.useState(1);const[L,j]=r.useState({categories:!1,subcategories:!1,faqs:!1,ticket:!1,chat:!1}),{club:C}=fe(),E=localStorage.getItem("user"),{register:Ce,handleSubmit:ve,reset:Ne}=Xe();h.useContext(ye);const{dispatch:Se}=h.useContext(re),[V,ke]=r.useState([]),[it,Ae]=r.useState([]),[p,G]=r.useState(!1),[v,te]=r.useState(null),[z,ae]=r.useState(!1),U=r.useRef(null),X=r.useRef(null),Me=()=>{X.current&&(X.current.scrollTop=X.current.scrollHeight)};r.useEffect(()=>{Re(),Te(),_e()},[C]),r.useEffect(()=>{const t=setInterval(()=>{V!=null&&V.setting_value&&ne(V)},6e4);return()=>clearInterval(t)},[V]);const Re=async()=>{j(t=>({...t,categories:!0}));try{d.setTable("faq_category");const t=await d.callRestAPI({},"GETALL");if(t.list){const s=t.list.filter(l=>l.club_id===0),i=t.list.filter(l=>l.club_id===(C==null?void 0:C.id));u([...s,...i])}}catch(t){console.error("Error loading FAQ categories:",t)}finally{j(t=>({...t,categories:!1}))}},ne=t=>{if(!(t!=null&&t.setting_value)){W(!1);return}try{const s=JSON.parse(t.setting_value),l=new Date().toTimeString().slice(0,8),c=s.some(m=>{const x=m.from,n=m.until;return n<x?l>=x||l<=n:l>=x&&l<=n});W(c)}catch(s){console.error("Error parsing working hours:",s),W(!1)}},Te=async()=>{try{const t=await ot.getOne("setting",5,{});console.log("result",t),t.model&&(ke(t.model),ne(t.model))}catch(t){console.error("Error fetching working hours:",t),W(!1)}},_e=async()=>{try{const t=await d.getMyRoom();if(console.log("Existing rooms response:",t),t&&t.list&&t.list.length>0){const i=t.list.sort((x,n)=>new Date(n.update_at)-new Date(x.update_at))[0];console.log("Last room:",i);const l=t.messages?t.messages.filter(x=>x.room_id===i.id):[],c=l.reduce((x,n)=>{const R=JSON.parse(n.chat);return n.unread===1&&R.user_id!==E.toString()?x+1:x},0),m={...i,unread:c,messages:l};Ae([m]),te(m),m.resolved===0?(console.log("Last room is open, setting up chat:",m.id),G(!0),q(m.id),$(m.chat_id),O(!0),D(!0),A(!0),await Le(m)):(console.log("Last room is resolved, showing FAQs only"),G(!1),q(null),$(null),O(!1),D(!1),A(!1))}else console.log("No rooms found, resetting states"),G(!1),te(null),q(null),$(null),O(!1),D(!1)}catch(t){console.error("Error fetching existing rooms:",t)}},Le=async t=>{try{const s=t.messages.map(l=>{const c=JSON.parse(l.chat);return{type:c.user_id===E.toString()?"user":"support",content:c.message,timestamp:new Date(c.timestamp||l.create_at)}}).sort((l,c)=>new Date(l.timestamp)-new Date(c.timestamp));a([{type:"system",content:"You have been reconnected to your previous conversation with support.",timestamp:new Date},...s]),await ie(t.id)}catch(s){console.error("Error loading existing messages:",s)}},Ee=async t=>{j(s=>({...s,subcategories:!0}));try{d.setTable("faq_subcategory");const s=await d.callRestAPI({filter:[`category_id,eq,${t}`]},"GETALL");s.list&&K(s.list)}catch(s){console.error("Error loading FAQ subcategories:",s)}finally{j(s=>({...s,subcategories:!1}))}},Be=async t=>{j(s=>({...s,faqs:!0}));try{d.setTable("faq");const s=await d.callRestAPI({filter:[`subcategory_id,eq,${t}`],join:["faq_subcategory|subcategory_id"]},"GETALL");s.list&&T(s.list)}catch(s){console.error("Error loading FAQs:",s)}finally{j(s=>({...s,faqs:!1}))}},Ie=t=>{f(t),y(null),T([]),Ee(t.id),a(s=>[...s,{type:"user",content:`I'll go with ${t.name}.`},{type:"bot",content:`Great! Here are some common questions related to ${t.name}:`}])},Pe=t=>{y(t),Be(t.id),a(s=>[...s,{type:"user",content:t.name},{type:"bot",content:"Here's the information you're looking for:"}])},Fe=async t=>{j(s=>({...s,ticket:!0}));try{d.setTable("ticket"),await d.callRestAPI({resolved:0,club_id:C==null?void 0:C.id,user_id:E,request:t.message},"POST"),be(!0),a(s=>[...s,{type:"bot",content:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(Je,{className:"mt-1 text-green-500"}),e.jsx("span",{children:"Your ticket has been created successfully. We'll get back to you via email shortly."})]})}]),I(!1),Ne()}catch(s){console.error("Error creating ticket:",s)}finally{j(s=>({...s,ticket:!1}))}},De=()=>{if(p&&v&&v.resolved===0){console.log("Continuing with existing open room");return}P&&F?(D(!0),A(!0),a(t=>[...t,{type:"bot",content:"I see! This sounds like something our support team can help with. Please type your message below and we'll connect you with a live support agent."}])):(I(!0),a(t=>[...t,{type:"bot",content:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(xe,{className:"mt-1 text-orange-500"}),e.jsx("span",{children:"Our support team is currently offline. Please create a ticket and we'll get back to you via email as soon as possible."})]})}]))};async function Oe(){try{const t=await d.callRawAPI("/v3/api/custom/courtmatchup/user/realtime/room",{user_id:E,other_user_id:1},"POST");return d.setTable("room"),await d.callRestAPI({id:t.room_id,admin_id:0,other_user_id:0},"PUT"),t}catch(t){console.log(t)}}const le=async()=>{if(B.length<1)return;if(!P||!F){a(s=>[...s,{type:"system",content:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(xe,{className:"mt-1 text-orange-500"}),e.jsx("span",{children:"Our support team is currently offline. Please create a ticket for assistance."})]}),timestamp:new Date}]),_(""),D(!1),I(!0);return}const t=B;a(s=>[...s,{type:"user",content:t,timestamp:new Date}]),_("");try{let s=new Date().toISOString().split("T")[0];j(c=>({...c,chat:!0}));let i=b,l=oe;if(console.log("Current room state:",{roomId:b,chatId:oe,hasOpenRoom:p,currentExistingRoom:v==null?void 0:v.id}),p&&v&&v.resolved===0)i?console.log("Using current room:",i):(console.log("Using existing room:",v.id),i=v.id,l=v.chat_id,q(i),$(l),O(!0));else if(!i||!l){console.log("Creating new room - no existing unresolved room found");const c=await Oe();i=c.room_id,l=c.chat_id,q(i),$(l),G(!0),te({id:i,chat_id:l,resolved:0}),M||(a(m=>[...m,{type:"system",content:"You are now connected to our support team. An agent will respond shortly.",timestamp:new Date}]),O(!0))}else console.log("Using current room:",i);await d.postMessage({room_id:i,chat_id:l,user_id:E,message:t,date:s}),!p&&M&&await ce()}catch(s){console.error(s),a(i=>[...i,{type:"system",content:"There was an error sending your message. Please try again.",timestamp:new Date}])}finally{j(s=>({...s,chat:!1}))}},He=()=>{f(null),y(null),T([]),a(t=>[...t,{type:"user",content:"Let's try a different category."},{type:"bot",content:"Please choose a category below to get started:"}])};async function ie(t){try{await d.callRestAPI({room_id:t,unread:0},"PUT")}catch(s){console.log("Error marking messages as read:",s)}}const se=()=>{U.current&&(clearTimeout(U.current),U.current=null),ae(!1)};async function ce(){if(z)return;ae(!0);const t=async()=>{try{(await d.startPooling(E)).message&&await Ze(),z&&p&&b&&(U.current=setTimeout(t,3e3))}catch(s){console.log(s.message),ge(Se,s.message),s.message==="TOKEN_EXPIRED"?window.location.replace("/user/login/"):z&&p&&b&&(U.current=setTimeout(t,5e3))}};t()}const Ze=async()=>{try{if(!b)return;const t=await d.getMyRoom();if(t&&t.list&&t.list.length>0){const s=t.list.find(i=>i.id===b);if(s){if(s.resolved===1){console.log("Room has been resolved by admin, hiding chat interface"),G(!1),O(!1),D(!1),A(!1),se(),a(n=>[...n,{type:"system",content:"This conversation has been resolved by our support team. You can start a new conversation if you need further assistance.",timestamp:new Date}]);return}const l=(t.messages?t.messages.filter(n=>n.room_id===s.id):[]).map(n=>{const R=JSON.parse(n.chat);return{id:n.id,type:R.user_id===E.toString()?"user":"support",content:R.message,timestamp:new Date(R.timestamp||n.create_at),user_id:R.user_id}}).sort((n,R)=>new Date(n.timestamp)-new Date(R.timestamp)),c=Z.filter(n=>n.type==="user"||n.type==="support"),m=new Set(c.map(n=>n.id).filter(Boolean)),x=l.filter(n=>!m.has(n.id)&&n.type==="support");x.length>0&&(a(n=>[...n,...x]),b&&await ie(b))}}}catch(t){console.error("Error refreshing room data:",t)}};return r.useEffect(()=>{Me()},[Z,je]),h.useEffect(()=>(p&&b&&M&&!z&&ce(),()=>{se()}),[p,b,M]),h.useEffect(()=>()=>{se()},[]),e.jsx(e.Fragment,{children:(C==null?void 0:C.supportchat_enabled)==1&&e.jsxs("div",{className:"fixed bottom-28 left-2 z-50 max-w-[220px] space-y-2",children:[N?e.jsx("button",{onClick:()=>S(!1),className:"shadow-full flex h-14 w-14 items-center justify-center rounded-full bg-primaryBlue p-2",children:e.jsx(Ue,{className:"text-xl text-white"})}):e.jsx("button",{onClick:()=>S(!0),className:"w-full rounded-xl bg-gray-100 p-2 shadow-lg transition-colors hover:bg-gray-200",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"text-2xl text-primaryBlue"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium",children:"Need help?"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Chat with assistant"})]})]})}),e.jsx(e.Fragment,{children:N&&e.jsxs("div",{className:"fixed bottom-48 left-2 w-[350px] rounded-xl bg-white shadow-lg",children:[e.jsx("div",{className:"rounded-t-2xl bg-primaryBlue p-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-2",children:M?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative",children:[e.jsx(ue,{className:"text-2xl text-white"}),e.jsx("div",{className:"absolute -bottom-1 -right-1 h-3 w-3 rounded-full bg-green-400 ring-2 ring-white"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium text-white",children:"Support Team"}),e.jsx("p",{className:"text-xs text-white opacity-80",children:"Online"})]})]}):e.jsxs(e.Fragment,{children:[e.jsx(me,{className:"text-2xl text-white"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium text-white",children:"AceBot"}),e.jsx("p",{className:"text-xs text-white opacity-80",children:P&&F?"Support Available":"Support Offline"})]})]})}),M?e.jsx("div",{className:"rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800",children:"Live Support"}):e.jsx("div",{className:`rounded-full px-2 py-1 text-xs font-medium ${P&&F?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"}`,children:P&&F?"Available":"Offline"})]})}),e.jsx("div",{ref:X,className:"h-[350px] overflow-y-auto p-4",children:e.jsxs("div",{className:"space-y-4",children:[!p&&e.jsxs("div",{className:"mb-6 flex items-start gap-2",children:[e.jsx("div",{className:"rounded-full bg-yellow-50 p-2",children:e.jsx(Qe,{className:"text-yellow-500"})}),e.jsx("div",{className:"max-w-[80%] rounded-lg bg-gray-50 p-3",children:e.jsxs("p",{className:"text-gray-800",children:["Hi there! 👋 How can I help you today?",e.jsx("br",{}),"Please choose a category below to get started:"]})})]}),Z.map((t,s)=>e.jsxs("div",{className:`flex items-start gap-2 ${t.type==="user"?"justify-end":""}`,children:[t.type==="bot"&&e.jsx("div",{className:"rounded-full bg-yellow-50 p-2",children:e.jsx(me,{className:"text-primaryBlue"})}),t.type==="support"&&e.jsx("div",{className:"rounded-full bg-green-50 p-2",children:e.jsx(ue,{className:"text-green-600"})}),t.type==="system"&&e.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:e.jsx(Ke,{className:"text-gray-600"})}),e.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${t.type==="user"?"bg-primaryBlue text-white":t.type==="support"?"bg-green-100 text-green-900":t.type==="system"?"border border-gray-200 bg-gray-100 text-gray-800":"bg-gray-50"}`,children:[e.jsx("p",{className:"whitespace-pre-wrap",children:t.content}),t.timestamp&&e.jsx("p",{className:"mt-1 text-xs opacity-70",children:new Date(t.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})]},s)),!p&&!g&&!w&&!Y&&e.jsx("div",{className:"flex flex-col items-end space-y-4",children:L.categories?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx(Q,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"w-full",children:[e.jsx("p",{className:"mb-2 text-right font-medium text-gray-700",children:"General"}),e.jsx("div",{className:"flex flex-col items-end space-y-2",children:H.filter(t=>t.club_id===0).map((t,s)=>e.jsxs("button",{onClick:()=>Ie(t),className:"flex w-fit items-center gap-2 rounded-lg bg-blue-50 px-4 py-2 text-left text-sm text-blue-900 transition-colors hover:bg-blue-100",children:[e.jsxs("span",{children:[s+1,"️⃣"]}),t.name]},t.id))})]})})}),!p&&g&&!k&&!w&&!Y&&e.jsxs("div",{className:"flex flex-col items-end space-y-2",children:[e.jsxs("button",{onClick:He,className:"mb-2 flex w-fit items-end justify-end gap-2 rounded-lg bg-gray-50 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100",children:[e.jsx(pe,{className:"text-gray-500"}),"Back to Categories"]}),L.subcategories?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx(Q,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):o.map(t=>e.jsx("button",{onClick:()=>Pe(t),className:"w-fit rounded-lg bg-blue-50 px-4 py-2 text-left text-sm text-blue-900 transition-colors hover:bg-blue-100",children:t.name},t.id))]}),!p&&k&&!w&&!Y&&e.jsxs("div",{className:"flex flex-col items-end space-y-4",children:[e.jsxs("button",{onClick:()=>{y(null),T([])},className:"mb-2 flex w-fit items-center gap-2 rounded-lg bg-gray-50 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100",children:[e.jsx(pe,{className:"text-gray-500"}),"Back to Questions"]}),L.faqs?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx(Q,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):ee.map(t=>e.jsxs("div",{className:"w-fit rounded-lg bg-gray-50 p-4 text-sm text-gray-900",children:[e.jsx("p",{className:"mb-2 font-medium",children:t.faq_subcategory.name}),e.jsx("p",{className:"mb-4 whitespace-pre-line",children:t.answer}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsx("div",{className:"text-center font-medium text-gray-700",children:"Problem still not resolved?"}),P&&F?e.jsx("button",{onClick:De,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Chat with Support"}):e.jsx("button",{onClick:()=>I(!0),className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Create Ticket"})]})]},t.id))]}),w&&!we&&e.jsxs("form",{onSubmit:ve(Fe),className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Please describe your issue"}),e.jsx("textarea",{...Ce("message",{required:!0}),rows:4,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Please provide details about your issue..."})]}),e.jsx("button",{type:"submit",disabled:L.ticket,className:"flex w-full items-center justify-center rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:L.ticket?e.jsx(Q,{className:"h-4 w-4 animate-spin"}):"Submit Ticket"})]})]})}),Y&&p&&J&&e.jsxs("div",{className:"border-t border-gray-200 bg-white p-3 shadow-md",children:[M&&e.jsx("div",{className:"mb-2 flex items-center justify-center",children:e.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-xs text-green-800",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Connected to Support Team"})]})}),e.jsxs("form",{onSubmit:t=>{t.preventDefault(),le()},className:"flex gap-2",children:[e.jsx("input",{type:"text",value:B,onChange:t=>_(t.target.value),placeholder:M?"Type your message to support...":"Type your message...",className:"flex-1 rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-primaryBlue",onKeyDown:t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),le())}}),e.jsx("button",{type:"submit",disabled:L.chat||B.trim()==="",className:"flex items-center justify-center rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:L.chat?e.jsx(Q,{className:"h-4 w-4 animate-spin"}):"Send"})]})]})]})})]})})},Ut=()=>{const{state:N}=h.useContext(ye),[S,g]=h.useState(!1);h.useState(!0);const{user_permissions:f}=fe(),k=qe(),{dispatch:y}=h.useContext(re),{showBackButton:H}=N,u=$e(),[o]=rt(),[K,ee]=r.useState({text:"",icon:null}),[T,J]=r.useState(!1),A=r.useRef(null),B=()=>{J(!T)};r.useEffect(()=>{const a=w=>{A.current&&!A.current.contains(w.target)&&J(!1)};return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[]),r.useEffect(()=>{const a=u.pathname,w=Ye.find(I=>a.includes(I.to));w&&ee({text:w.text,icon:w.icon})},[u]);const _=localStorage.getItem("role"),Z=[{text:"Profile details",icon:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.88083 15.7631C5.97548 14.164 7.77671 13.125 10 13.125C12.2233 13.125 14.0246 14.164 15.1192 15.7631M4.88083 15.7631C6.2422 16.9732 8.03527 17.7083 10 17.7083C11.9648 17.7083 13.7578 16.9732 15.1192 15.7631M4.88083 15.7631C3.2924 14.3511 2.29169 12.2924 2.29169 9.99996C2.29169 5.74276 5.74283 2.29163 10 2.29163C14.2572 2.29163 17.7084 5.74276 17.7084 9.99996C17.7084 12.2924 16.7076 14.3511 15.1192 15.7631M12.7084 8.33329C12.7084 9.82906 11.4958 11.0416 10 11.0416C8.50425 11.0416 7.29169 9.82906 7.29169 8.33329C7.29169 6.83752 8.50425 5.62496 10 5.62496C11.4958 5.62496 12.7084 6.83752 12.7084 8.33329Z",stroke:"#868C98","stroke-width":"1.5","stroke-linejoin":"round"})}),to:"/user/profile"},{text:"Payment methods",icon:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M2.29169 8.12496V15.205C2.29169 15.6653 2.66478 16.0384 3.12502 16.0384L16.8718 16.0384C17.332 16.0384 17.7051 15.6653 17.7051 15.205V8.12496M2.29169 8.12496V4.79403C2.29169 4.33379 2.66478 3.96069 3.12502 3.96069H16.8709C17.3302 3.96069 17.703 4.33231 17.7037 4.79161C17.7056 5.90272 17.7051 7.01384 17.7051 8.12496M2.29169 8.12496H17.7051M5.62502 11.0416H8.12502",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),to:"/user/profile?tab=payment-methods"},{text:"Membership",icon:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.45831 12.4999V18.0933C6.45831 18.3988 6.77604 18.6004 7.05239 18.4704L9.82256 17.1667C9.93493 17.1139 10.065 17.1139 10.1774 17.1667L12.9476 18.4704C13.2239 18.6004 13.5416 18.3988 13.5416 18.0933V12.4999M16.0416 7.49992C16.0416 10.8366 13.3367 13.5416 9.99998 13.5416C6.66326 13.5416 3.95831 10.8366 3.95831 7.49992C3.95831 4.1632 6.66326 1.45825 9.99998 1.45825C13.3367 1.45825 16.0416 4.1632 16.0416 7.49992Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),to:"/user/profile?tab=membership"},{text:"Billing ",icon:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.00002 5.14591V4.28943M9.00002 12.8542V13.7107M10.8548 6.43064C10.4845 5.91863 9.79257 5.57416 9.00002 5.57416H8.76211C7.71095 5.57416 6.85882 6.25586 6.85882 7.09679V7.16216C6.85882 7.76365 7.28361 8.31352 7.9561 8.58251L10.0439 9.41765C10.7164 9.68665 11.1412 10.2365 11.1412 10.838C11.1412 11.715 10.2525 12.426 9.15622 12.426H9.00002C8.20747 12.426 7.5155 12.0815 7.14527 11.5695M16.7084 9.00008C16.7084 13.2573 13.2572 16.7084 9.00002 16.7084C4.74283 16.7084 1.29169 13.2573 1.29169 9.00008C1.29169 4.74289 4.74283 1.29175 9.00002 1.29175C13.2572 1.29175 16.7084 4.74289 16.7084 9.00008Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),to:"/user/profile?tab=billing"},{text:"Get the app",icon:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.95835 3.54159H11.0417M5.62502 18.5416H14.375C14.8353 18.5416 15.2084 18.1685 15.2084 17.7083V2.29159C15.2084 1.83135 14.8353 1.45825 14.375 1.45825H5.62502C5.16478 1.45825 4.79169 1.83135 4.79169 2.29159V17.7083C4.79169 18.1685 5.16478 18.5416 5.62502 18.5416Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),onClick:()=>g(!0)},{text:"Logout",icon:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M13.3333 5.41666C13.3333 7.25761 11.8409 8.74999 9.99997 8.74999C8.15902 8.74999 6.66664 7.25761 6.66664 5.41666C6.66664 3.57571 8.15902 2.08332 9.99997 2.08332C11.8409 2.08332 13.3333 3.57571 13.3333 5.41666Z",fill:"#A8A8A8"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99997 2.49999C8.38914 2.49999 7.08331 3.80583 7.08331 5.41666C7.08331 7.02749 8.38914 8.33332 9.99997 8.33332C11.6108 8.33332 12.9166 7.02749 12.9166 5.41666C12.9166 3.80583 11.6108 2.49999 9.99997 2.49999ZM6.24997 5.41666C6.24997 3.34559 7.9289 1.66666 9.99997 1.66666C12.071 1.66666 13.75 3.34559 13.75 5.41666C13.75 7.48772 12.071 9.16666 9.99997 9.16666C7.9289 9.16666 6.24997 7.48772 6.24997 5.41666Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M9.99997 10.4167C6.27535 10.4167 3.66126 13.3457 3.33331 17.0833H16.6666C16.3387 13.3457 13.7246 10.4167 9.99997 10.4167Z",fill:"#A8A8A8"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.80032 16.6667H16.1996C15.725 13.323 13.3164 10.8333 9.99997 10.8333C6.68352 10.8333 4.27494 13.323 3.80032 16.6667ZM2.91823 17.0469C3.26095 13.1409 6.01533 9.99999 9.99997 9.99999C13.9846 9.99999 16.739 13.1409 17.0817 17.0469L17.1215 17.5H2.87848L2.91823 17.0469Z",fill:"#A8A8A8"})]}),to:"/user/login",onClick:()=>{y({type:"LOGOUT"})}}];return e.jsxs("div",{className:"relative",children:[S&&e.jsx(ze,{onClose:()=>g(!1)}),e.jsxs("div",{className:"sticky top-0 z-50 flex h-14 w-full items-center justify-between border-b border-gray-200 bg-white px-6 py-8 ",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[H&&e.jsx(We,{}),e.jsxs("div",{className:"flex items-center gap-2",children:[K.icon,e.jsx("p",{className:"text-lg font-medium text-gray-700",children:K.text})]})]}),e.jsxs("div",{className:"flex items-center gap-3 pr-2",children:[_==="user"&&(f==null?void 0:f.allowCourt)&&e.jsxs("button",{onClick:()=>k("/user/reserve-court"),className:"hidden items-center gap-2 whitespace-nowrap rounded-xl bg-navy-700 px-3.5 py-1.5 text-sm text-white sm:flex",children:[e.jsx("span",{className:"text-lg",children:"+"}),"Reserve a court"]}),e.jsxs("div",{ref:A,className:"relative",children:[e.jsxs("button",{className:"flex items-center gap-2",onClick:B,children:[e.jsx("div",{title:"Profile",className:"peer",children:e.jsx("img",{className:"h-[30px] w-[30px] rounded-full object-cover",src:(o==null?void 0:o.photo)||"/default-avatar.png",alt:`${o==null?void 0:o.first_name} ${o==null?void 0:o.last_name}`})}),e.jsx("p",{className:"hidden max-w-[120px] truncate text-sm font-medium text-black sm:block",children:o!=null&&o.first_name&&(o!=null&&o.last_name)?`${o==null?void 0:o.first_name} ${o==null?void 0:o.last_name}`:"Profile"}),e.jsx(st,{className:"h-4 w-4"})]}),e.jsx("ul",{className:`absolute right-0 top-[100%] z-20 mt-2 sm:right-5 ${T?"block":"hidden"} min-w-[200px] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-[#525252] shadow-lg`,children:_=="user"&&Z.map(a=>e.jsxs("button",{className:"hover:text[#262626] flex w-full cursor-pointer items-center rounded-md px-2 py-3 hover:bg-[#F4F4F4]",onClick:()=>{a.onClick&&typeof a.onClick=="function"&&a.onClick(),a.to&&typeof a.to=="string"&&k(a.to)},children:[e.jsx("div",{className:"mr-2",children:a.icon}),e.jsx("span",{className:"truncate",children:a.text})]},a.text))||e.jsxs(e.Fragment,{children:[e.jsx("li",{children:e.jsxs(de,{className:"hover:text[#262626] flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4]",to:`/${o==null?void 0:o.role}/profile`,children:[e.jsx("span",{className:"mr-2",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:[e.jsx("path",{d:"M13.3333 5.41666C13.3333 7.25761 11.8409 8.74999 9.99997 8.74999C8.15902 8.74999 6.66664 7.25761 6.66664 5.41666C6.66664 3.57571 8.15902 2.08332 9.99997 2.08332C11.8409 2.08332 13.3333 3.57571 13.3333 5.41666Z",fill:"#A8A8A8"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.99997 2.49999C8.38914 2.49999 7.08331 3.80583 7.08331 5.41666C7.08331 7.02749 8.38914 8.33332 9.99997 8.33332C11.6108 8.33332 12.9166 7.02749 12.9166 5.41666C12.9166 3.80583 11.6108 2.49999 9.99997 2.49999ZM6.24997 5.41666C6.24997 3.34559 7.9289 1.66666 9.99997 1.66666C12.071 1.66666 13.75 3.34559 13.75 5.41666C13.75 7.48772 12.071 9.16666 9.99997 9.16666C7.9289 9.16666 6.24997 7.48772 6.24997 5.41666Z",fill:"#A8A8A8"}),e.jsx("path",{d:"M9.99997 10.4167C6.27535 10.4167 3.66126 13.3457 3.33331 17.0833H16.6666C16.3387 13.3457 13.7246 10.4167 9.99997 10.4167Z",fill:"#A8A8A8"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.80032 16.6667H16.1996C15.725 13.323 13.3164 10.8333 9.99997 10.8333C6.68352 10.8333 4.27494 13.323 3.80032 16.6667ZM2.91823 17.0469C3.26095 13.1409 6.01533 9.99999 9.99997 9.99999C13.9846 9.99999 16.739 13.1409 17.0817 17.0469L17.1215 17.5H2.87848L2.91823 17.0469Z",fill:"#A8A8A8"})]})}),e.jsx("span",{children:"Account"})]})}),e.jsx("li",{children:e.jsxs(de,{className:"hover:text[#262626] group flex cursor-pointer items-center rounded-md px-4 py-3 hover:bg-[#F4F4F4] hover:text-red-500",to:`/${o==null?void 0:o.role}/login`,onClick:()=>y({type:"LOGOUT"}),children:[e.jsx("span",{className:"mr-2",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{className:"group-hover:fill-[#ef4444] group-hover:stroke-[#ef4444]",fillRule:"evenodd",clipRule:"evenodd",d:"M3.75 3.33333C3.51988 3.33333 3.33333 3.51988 3.33333 3.75L3.33333 16.25C3.33333 16.4801 3.51988 16.6667 3.75 16.6667H9.58333C9.81345 16.6667 10 16.8532 10 17.0833C10 17.3135 9.81345 17.5 9.58333 17.5H3.75C3.05964 17.5 2.5 16.9404 2.5 16.25L2.5 3.75C2.5 3.05964 3.05964 2.5 3.75 2.5L9.58333 2.5C9.81345 2.5 10 2.68655 10 2.91667C10 3.14679 9.81345 3.33333 9.58333 3.33333L3.75 3.33333ZM13.0387 5.95537C13.2014 5.79265 13.4652 5.79265 13.628 5.95537L17.378 9.70536C17.5407 9.86808 17.5407 10.1319 17.378 10.2946L13.628 14.0446C13.4652 14.2073 13.2014 14.2073 13.0387 14.0446C12.876 13.8819 12.876 13.6181 13.0387 13.4554L16.0774 10.4167L7.91667 10.4167C7.68655 10.4167 7.5 10.2301 7.5 9.99999C7.5 9.76987 7.68655 9.58332 7.91667 9.58332L16.0774 9.58332L13.0387 6.54463C12.876 6.38191 12.876 6.11809 13.0387 5.95537Z",fill:"#A8A8A8",stroke:"#A8A8A8",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("span",{children:"Logout"})]})})]})})]})]})]}),_=="user"&&e.jsx(at,{})]})};export{Ut as default};
