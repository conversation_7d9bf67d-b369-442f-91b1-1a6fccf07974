import{j as i}from"./@nivo/heatmap-ba1ecfff.js";import{b as o,u as I,f as G,r}from"./vendor-851db8c1.js";import"./yup-54691517.js";import{M as j,T as M,G as v,A as D,e as U}from"./index-3b44b02c.js";import"./react-quill-73fb9518.js";/* empty css                   */import{C as k}from"./ClubSelection-3530ed28.js";import{A as B}from"./AddBuddy-62b32f68.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./yup-2824f222.js";import"./@hookform/resolvers-67648cca.js";import"./Calendar-9031b5fe.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./BackButton-11ba52b2.js";import"./SuccessModal-e9ef416e.js";import"./TimeSlots-190fee7e.js";import"./SportTypeSelection-ee0cc3da.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ReservationSummary-260408bc.js";let s=new j;new M;const Kt=({setSidebar:K})=>{const{dispatch:m,state:N}=o.useContext(v);I(),o.useContext(D),G(),o.useState([]);const[u,d]=o.useState([]),[f,S]=o.useState([]),[h,A]=o.useState([]),[$,p]=r.useState(!1),[g,w]=r.useState([]),[x,n]=r.useState(!0),[C,l]=r.useState(null),[L,c]=r.useState(!1),[y,T]=r.useState(null),E=async t=>{p(!0);try{s.setTable("user");const e=await s.callRestAPI({filter:["role,cs,user"]},"GETALL");s.setTable("user");const a=await s.callRestAPI({filter:["role,cs,coach"]},"GETALL");d(e.list),w(a.list)}catch(e){return console.error("Error fetching users:",e),[]}finally{p(!1)}},R=async t=>{console.log("clubId",t);try{s.setTable("sports");const e=await s.callRestAPI({filter:[`club_id,eq,${parseInt(t)}`]},"GETALL");S(e.list),s.setTable("surface");const a=await s.callRestAPI({},"GETALL");A(a.list)}catch(e){return console.error("Error fetching sports:",e),[]}},P=async t=>{try{const e=await s.callRawAPI(`/v3/api/custom/courtmatchup/club/profile/${t}`,{},"GET");T(e.model)}catch(e){return console.error("Error fetching club profile:",e),null}};o.useEffect(()=>{m({type:"SETPATH",payload:{path:"find-a-buddy"}})},[]);const b=async t=>{c(!0),l(t),await E(t==null?void 0:t.id),await R(t==null?void 0:t.id),await P(t==null?void 0:t.id),n(!1),c(!1)};return i.jsxs("div",{className:"mx-auto max-w-7xl p-4",children:[L&&i.jsx(U,{}),i.jsx(B,{sports:f,surfaces:h,users:u,coaches:g,role:"admin",profileSettings:y}),x&&i.jsx(k,{setSelectedClub:l,selectedClub:C,setShow:n,onAction:b})]})};export{Kt as default};
