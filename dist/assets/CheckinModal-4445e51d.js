import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as t,L}from"./vendor-851db8c1.js";import{B as ae}from"./BottomDrawer-f0d615b3.js";import{M as I,G as F,$ as ne,a0 as ie,a1 as le,v as oe,d as re,H as Z,E as O,J as V,b as S,a2 as ce}from"./index-3b44b02c.js";import{B as de}from"./BackButton-11ba52b2.js";import{T as me}from"./TimeSlots-190fee7e.js";import{C as ue}from"./Calendar-9031b5fe.js";import{S as he}from"./SportTypeSelection-ee0cc3da.js";import{C as xe}from"./ReservationSummary-260408bc.js";let B=new I;const ke=({isOpen:j,onClose:u,players:n,sports:b,club:l})=>{t.useState("selection");const[N,r]=t.useState(null);t.useState(null),t.useState(null);const[d,x]=t.useState(null),[h,g]=t.useState(new Date);t.useState(null);const[m,v]=t.useState(0),[a,c]=t.useState(0),[f,H]=t.useState(null),[_,J]=t.useState(null),{state:ge,dispatch:k}=t.useContext(F),[T,M]=t.useState(!1),[U,fe]=t.useState({from:null,until:null}),[w,D]=t.useState("main"),[$,z]=t.useState(""),[i,G]=t.useState([]),[P,Q]=t.useState([]);t.useEffect(()=>{},[w]);const Y=()=>{const s=new Date(h);s.setMonth(s.getMonth()-1),g(s)},K=()=>{const s=new Date(h);s.setMonth(s.getMonth()+1),g(s)},X=s=>{x(s)},{duration:pe,end_time:q,start_time:W}=ne(P),R=async()=>{M(!0);try{const s={sport_id:N,type:f,surface:_,date:d,player_ids:i.map(y=>y.id),start_time:W,end_time:q,price:a};console.log("form data",s);const o=await B.callRawAPI("/v3/api/custom/courtmatchup/club/reservations",s,"POST");await Z(B,{user_id:localStorage.getItem("user"),activity_type:O.court_reservation,action_type:V.CREATE,data:s,club_id:l==null?void 0:l.id,description:"Created a court reservation"}),o.error||(S(k,"Reservation created successfully",3e3,"success"),u())}catch(s){console.error(s),S(k,s.message||"Error creating reservation",3e3,"error")}finally{M(!1)}},ee=s=>{Q([{from:s.from,until:s.until}])},A=s=>{G(o=>o.some(C=>C.id===s.id)?o.filter(C=>C.id!==s.id):[...o,s])},se=s=>i.some(o=>o.id===s),E=n.filter(s=>`${s.first_name} ${s.last_name}`.toLowerCase().includes($.toLowerCase())),te=()=>{D("players")};return t.useEffect(()=>{m&&(i!=null&&i.length)?c(m*(i==null?void 0:i.length)):c(m)},[m,i]),e.jsxs(ae,{isOpen:j,onClose:u,title:w==="main"?"Add New Reservation":"Select Players",showActions:!1,saveLabel:"Create Reservation",onSave:R,isSubmitting:T,children:[e.jsx(de,{onBack:()=>{w=="main"?u():D("main")}}),w==="main"?e.jsx("div",{className:" p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(he,{sports:b,onSelectionChange:({sport:s,type:o,subType:y})=>{r(s),H(o),J(y)}}),e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsx(ue,{currentMonth:h,selectedDate:d,onDateSelect:X,onPreviousMonth:Y,onNextMonth:K,allowPastDates:!1,minDate:new Date,maxDate:new Date(new Date().setMonth(new Date().getMonth()+3)),daysOff:l!=null&&l.days_off?JSON.parse(l.days_off):[]})}),d&&e.jsx(me,{onTimeClick:ee,selectedDate:d,timeRange:U,timeSlots:ie(),onNext:te,nextButtonText:"Next",startHour:0,endHour:24,interval:30,className:"h-fit rounded-lg bg-white p-4 shadow-5",multipleSlots:!1,individualSelection:!0,isTimeSlotAvailable:s=>!0,clubTimes:l!=null&&l.times?JSON.parse(l.times):[]})]})})})}):e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(xe,{selectedSport:N,sports:b,selectedType:f,selectedSubType:_,selectedDate:d,selectedTimes:P}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reservation for:"})}),e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex items-center gap-1 rounded-lg border border-gray-300 px-2 focus:border-blue-500 focus:outline-none",children:[e.jsx("span",{className:"w-5",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search by name",value:$,onChange:s=>z(s.target.value),className:"w-full border-none bg-transparent focus:outline-none focus:ring-0"})]})}),i.length>0&&e.jsx("div",{className:"mb-4 flex flex-wrap gap-2",children:i.map(s=>e.jsxs("div",{className:"flex items-center gap-2 rounded-lg bg-gray-100 px-3 py-1",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm",children:`${s.first_name} ${s.last_name}`}),e.jsx("button",{onClick:o=>{o.stopPropagation(),A(s)},className:"text-gray-400 hover:text-gray-600",children:e.jsx(le,{className:"h-4 w-4"})})]},s.id))}),e.jsx("div",{className:"max-h-64 space-y-2 overflow-y-auto rounded-xl bg-gray-50",children:E.length>0?E.map(s=>e.jsxs("div",{onClick:()=>A(s),className:"flex cursor-pointer items-center space-x-3 rounded-lg p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",checked:se(s.id),onChange:()=>{},className:"h-4 w-4 rounded border-gray-300 text-blue-600"}),e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full bg-gray-200",children:e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"})}),e.jsx("span",{children:`${s.first_name} ${s.last_name}`})]},s.id)):e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No players found"})})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm text-gray-500",children:["PLAYERS (",i.length,")"]}),e.jsx("div",{className:"mt-1",children:i.map(s=>e.jsxs("div",{className:"text-sm",children:[s.first_name," ",s.last_name]},s.id))})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{children:"Fee"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"$"}),e.jsx("input",{type:"text",className:"w-20 rounded-lg border border-gray-200 bg-gray-50 px-2 py-1 text-right",value:m,onChange:s=>{v(s.target.value)}})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:oe(a)})]}),e.jsx("div",{className:"rounded-lg bg-blue-700 p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:e.jsx("path",{d:"M10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2ZM0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM9 5C9 4.44772 9.44772 4 10 4C10.5523 4 11 4.44772 11 5V11C11 11.5523 10.5523 12 10 12C9.44772 12 9 11.5523 9 11V5ZM9 15C9 14.4477 9.44772 14 10 14C10.5523 14 11 14.4477 11 15C11 15.5523 10.5523 16 10 16C9.44772 16 9 15.5523 9 15Z",fill:"currentColor"})}),e.jsx("span",{children:i.length>0?`${i.map(s=>`${s.first_name} ${s.last_name}`).join(", ")} will be notified about this reservation.`:"selected players will be notified about this reservation."})]})}),e.jsx(re,{loading:T,onClick:R,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Make reservation"}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"}),e.jsxs("p",{className:"text-sm text-gray-500",children:['By clicking "Make reservation" you agree to our'," ",e.jsx(L,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(L,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise. For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"}),"."]})]})})]})]})]})};let p=new I;function Te({courts:j,onClose:u,reservation:n,getData:b,sports:l,setReservation:N}){var v;const[r,d]=t.useState(((v=j[0])==null?void 0:v.id)||null),[x,h]=t.useState(!1),{dispatch:g}=t.useContext(F);console.log(n);const m=async()=>{var a;if(!(!r&&!n)){h(!0);try{p.setTable("reservation");const c=await p.callRestAPI({id:Number(n==null?void 0:n.id),check_in:1},"PUT");p.setTable("booking"),await p.callRestAPI({id:n.booking_id,court_id:r},"PUT"),await Z(p,{user_id:localStorage.getItem("user"),activity_type:O.court_reservation,action_type:V.UPDATE,data:{reservation_id:n.id,court_id:r},club_id:n==null?void 0:n.club_id,description:`Checked in for ${(a=n==null?void 0:n.booking)==null?void 0:a.player_ids}`}),S(g,"Checked in successfully",3e3,"success"),N(f=>({...f,check_in:1,booking:{...f.booking,court_id:r}})),b(1)}catch(c){S(g,c==null?void 0:c.message,3e3,"error"),console.log(c)}finally{h(!1),u()}}};return console.log(r),e.jsxs("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Check In"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Please select a court to check in:"}),e.jsx("div",{className:"mb-6 max-h-60 overflow-y-auto px-2",children:j.map(a=>e.jsxs("div",{className:"mb-2 flex items-center",children:[e.jsx("input",{type:"radio",id:`court-${a.id}`,name:"court",value:a.id,checked:r===a.id,onChange:()=>d(a.id),className:"h-4 w-4 text-blue-600 focus:ring-blue-500"}),e.jsxs("label",{htmlFor:`court-${a.id}`,className:"ml-2 flex items-center rounded-lg p-2 hover:bg-gray-50",children:[e.jsx(ce,{className:"mr-2 text-blue-500"}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:a.name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(",l.find(c=>c.id==a.sport_id).name,a.type&&` • ${a.type}`,a.sub_type&&` • ${a.sub_type}`,")"]})]})]})]},a.id))}),e.jsxs("div",{className:"flex justify-end gap-3 border-t pt-4",children:[e.jsx("button",{onClick:u,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:m,disabled:!r||x,className:`rounded-lg px-4 py-2 text-white ${r?"bg-blue-600 hover:bg-blue-700":"cursor-not-allowed bg-gray-400"} ${x?"cursor-wait opacity-75":""}`,children:x?"Processing...":"Check In"})]})]})]})}export{ke as A,Te as C};
