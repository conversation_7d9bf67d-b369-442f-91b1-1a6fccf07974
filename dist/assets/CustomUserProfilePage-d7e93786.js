import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as $,r as a,f as fe,L as ue,k as Se}from"./vendor-851db8c1.js";import{u as le}from"./react-hook-form-687afde5.js";import{o as ne}from"./yup-2824f222.js";import{c as ie,a as oe,e as ge}from"./yup-54691517.js";import{M as K,T as be,A as ye,G as H,e as ee,d as ce,t as z,b as u,n as Ce,o as Pe,R as ke,p as _e,u as Ie,q as Ee,r as Ae,v as Te,f as Fe,h as Me,x as $e,F as Be,y as De}from"./index-3b44b02c.js";import"./index-02625b16.js";import{I as Le}from"./ImageCropModal-44851a76.js";import{F as Ue,a as ze}from"./index.esm-51ae62c8.js";import{S as Re}from"./index.esm-92169588.js";import{b as Oe}from"./index.esm-c561e951.js";import{u as je,a as Ze,C as he}from"./@stripe/react-stripe-js-64f0e61f.js";import{L as Ge}from"./index.esm-3a36c7d6.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{B as qe}from"./BackButton-11ba52b2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";let J=new K,He=new be;const Ve=()=>{const g=ie({email:oe().email().required()}).required(),{dispatch:v}=$.useContext(ye),[w,b]=a.useState("");$.useState({});const[S,_]=a.useState("");a.useState(!1);const[x,h]=a.useState(!1),[m,P]=a.useState({}),[U,C]=a.useState(!0),[G,I]=a.useState(null),[A,p]=a.useState(""),{dispatch:y}=$.useContext(H),[j,T]=a.useState(!1),[f,F]=a.useState(null),[B,M]=a.useState(!1),[E,N]=a.useState(null),{register:D,handleSubmit:s,setError:l,setValue:i,formState:{errors:o}}=le({resolver:ne(g)}),R=localStorage.getItem("user");async function O(){var c;C(!0);try{const n=await He.getList("profile",{filter:[`user_id,eq,${R}`],join:["user|user_id"]}),r=(c=n==null?void 0:n.list)==null?void 0:c[0];if(r){const t=r.user||{},d=r.id,k={...r,...t,profile_id:d,user_id:t.id};P(k),i("email",t==null?void 0:t.email),i("first_name",t==null?void 0:t.first_name),i("last_name",t==null?void 0:t.last_name),i("phone",t==null?void 0:t.phone),i("bio",t==null?void 0:t.bio),b(t==null?void 0:t.email),_(t==null?void 0:t.photo),i("gender",r==null?void 0:r.gender),i("address",r==null?void 0:r.address),i("city",r==null?void 0:r.city),i("state",r==null?void 0:r.state),i("zip_code",r==null?void 0:r.zip_code),i("ntrp",r==null?void 0:r.ntrp),v({type:"UPDATE_PROFILE",payload:k}),C(!1)}}catch(n){z(v,n.response.data.message?n.response.data.message:n.message)}}const Z=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],V=["gender","address","city","state","zip_code","ntrp","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:m==null?void 0:m.user_id,profile_id:m==null?void 0:m.profile_id,defaultValues:m});const Y=async(c,n)=>{try{h(!0);const r={[c]:n},t=Z.includes(c),d=V.includes(c);if(t){J.setTable("user");const k=await J.callRestAPI({id:m==null?void 0:m.user_id,...r},"PUT");k.error?q(k):(u(y,"Profile Updated",4e3),I(null),p(""),O())}else if(d){J.setTable("profile");const k=await J.callRestAPI({id:m==null?void 0:m.profile_id,...r},"PUT");k.error?q(k):(u(y,"Profile Updated",4e3),I(null),p(""),O())}else{u(y,"Unknown field type: "+c,4e3,"error"),h(!1);return}h(!1)}catch(r){h(!1),l(c,{type:"manual",message:r!=null&&r.message&&r==null?void 0:r.message}),z(v,r!=null&&r.message&&r==null?void 0:r.message)}},q=c=>{if(c.validation){const n=Object.keys(c.validation);for(let r=0;r<n.length;r++){const t=n[r];l(t,{type:"manual",message:c.validation[t]})}}},L=c=>{try{if(c.size>2*1024*1024){u(y,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}N(c.type);const n=new FileReader;n.onload=()=>{F(n.result),M(!0)},n.readAsDataURL(c)}catch(n){u(y,n==null?void 0:n.message,3e3,"error"),console.log(n)}},Q=async c=>{try{T(!0);const n=E==="image/png",r=new File([c],`cropped_profile.${n?"png":"jpg"}`,{type:n?"image/png":"image/jpeg"});let t=new FormData;t.append("file",r);let d=await J.uploadImage(t);Y("photo",d==null?void 0:d.url)}catch(n){u(y,n==null?void 0:n.message,3e3,"error"),console.log(n)}finally{T(!1)}},se=()=>{Y("photo",null),P({...m,photo:null})};return $.useEffect(()=>{O()},[]),e.jsxs("div",{className:"",children:[U||j&&e.jsx(ee,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(Le,{isOpen:B,onClose:()=>M(!1),image:f,onCropComplete:Q}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:S||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:se,disabled:!S,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:c=>L(c.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"ntrp",label:"NTRP"},{key:"bio",label:"Bio",type:"textarea"}].map(c=>e.jsx("div",{children:G===c.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:c.label}),e.jsx("button",{onClick:()=>I(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),c.type==="select"?e.jsxs("select",{value:A,onChange:n=>p(n.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",c.label.toLowerCase()]}),c.options.map(n=>e.jsx("option",{value:n,children:n.charAt(0).toUpperCase()+n.slice(1)},n))]}):c.type==="textarea"?e.jsx("textarea",{value:A,onChange:n=>p(n.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:A,onChange:n=>p(n.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),c.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:c.note}),e.jsx("div",{className:"mt-2",children:e.jsx(ce,{loading:x,onClick:()=>Y(c.key,A),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:c.label}),e.jsx("button",{onClick:()=>{I(c.key),p((m==null?void 0:m[c.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(m==null?void 0:m[c.key])||"--"}),c.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:c.note})]})},c.key))})]})]})})]})};let Ye=new K;function Je({getData:g,onClose:v}){const[w,b]=a.useState(!1),S=je(),_=Ze(),{dispatch:x}=a.useContext(H),h=ie({user:ge(),token:oe()}),{register:m,setValue:P,handleSubmit:U,setError:C,formState:{errors:G}}=le({resolver:ne(h)}),I=async A=>{b(!0),S.createToken(_.getElement(he)).then(async p=>{if(console.log(p),p.error){u(x,p.error||"Something went wrong");return}const y={sourceToken:p.token.id};try{const j=await Ye.createCustomerStripeCard(y);if(!j.error)u(x,"Card added successfully");else if(j.validation){const T=Object.keys(j.validation);for(let f=0;f<T.length;f++){const F=T[f];u(x,j.validation[F],3e3)}}g(),v()}catch(j){console.error(j),u(x,j.message,5e3),z(x,j.code)}finally{b(!1)}})};return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-5",children:e.jsxs("form",{className:"",onSubmit:U(I),children:[e.jsx(he,{className:"mb-3 rounded p-4 shadow-inner",options:{hidePostalCode:!0,style:{base:{backgroundColor:"",fontSize:"14px",lineHeight:"20px"}}}}),e.jsx(ce,{loading:w,type:"submit",className:"inline-block rounded-lg bg-primaryBlue px-3 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:w?"Adding...":"Add card"})]})})})}let ae=new K;function Ke(){var D;const[g,v]=a.useState("bank_cards"),[w,b]=a.useState(!1),[S,_]=a.useState(!1),[x,h]=a.useState(""),[m,P]=a.useState(10),[U,C]=a.useState(!1),[G,I]=a.useState(!1),[A,p]=a.useState(!1),[y,j]=$.useState({});je();const T=ie({user:ge(),token:oe()}),{dispatch:f}=$.useContext(H);le({resolver:ne(T)});const F=[{id:"bank_cards",label:"Bank cards",icon:e.jsx(_e,{})}],B=async s=>{_(!0);try{console.log("Saving card:",s),await new Promise(l=>setTimeout(l,1e3)),b(!1)}catch(l){console.error("Error saving card:",l)}finally{_(!1)}};async function M(s){var l,i;try{p(!0);const{data:o,limit:R,error:O,message:Z}=await ae.getCustomerStripeCards(s);if(console.log(o),O&&u(f,Z,5e3),!o)return;x||h(((l=o==null?void 0:o.data[0])==null?void 0:l.id)??""),j(o),P(+R),C(x&&x!==((i=o.data[0])==null?void 0:i.id)),I(o.has_more)}catch(o){console.error("ERROR",o),u(f,o.message,5e3),z(dispatch,o.code)}finally{p(!1)}}const E=async s=>{p(!0);const{error:l,message:i}=await ae.setStripeCustomerDefaultCard(s);if(u(f,i),l){console.error(l);return}M({}),p(!1)},N=async s=>{p(!0);const{isDeleted:l,error:i,message:o}=await ae.deleteCustomerStripeCard(s);if(u(f,o),i){console.error(i);return}M({}),p(!1)};return $.useEffect(()=>{M({})},[]),e.jsxs("div",{className:"mx-auto max-w-2xl p-4 sm:p-6",children:[e.jsx("h2",{className:"mb-6 text-2xl font-semibold",children:"Payment methods"}),A&&e.jsx(ee,{}),e.jsx("div",{className:"mb-6 flex flex-wrap gap-4 border-b sm:flex-nowrap sm:gap-0 sm:space-x-4",children:F.map(s=>e.jsxs("button",{onClick:()=>v(s.id),className:`flex items-center space-x-2 px-1 pb-2 ${g===s.id?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:s.icon}),e.jsx("span",{children:s.label})]},s.id))}),g==="bank_cards"&&e.jsxs("div",{className:"space-y-4",children:[(D=y==null?void 0:y.data)==null?void 0:D.map(s=>e.jsxs("div",{className:"flex flex-col justify-between gap-3 rounded-xl border p-4",children:[e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"text-sm text-gray-700 sm:text-base",children:s.customer.email}),e.jsx("div",{className:"flex items-center justify-end space-x-4",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>E(s.id),className:`text-sm sm:text-base ${s.id===s.customer.default_source?"text-green-600":"text-blue-600"} underline`,children:s.id===s.customer.default_source?"Default":"Set Default"}),e.jsx("button",{onClick:()=>N(s.id),className:"text-sm text-gray-500 underline sm:text-base",children:"Delete"})]})})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex space-x-2",children:[s.brand==="Visa"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#1A1F71] text-white",children:e.jsx(Oe,{size:18})}),s.brand==="Mastercard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#EB001B] text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:"fill: none"}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:"fill: #ff5f00"}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:"fill: #eb001b"}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"})]})]})})}),s.brand==="MasterCard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded  text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:{fill:"none"}}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:{fill:"#ff5f00"}}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#eb001b"}}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}})]})]})})}),s.brand==="American Express"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#006FCF] text-white",children:e.jsx(Re,{size:18})}),s.brand==="Discover"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#FF6000] text-sm font-bold text-white",children:"DISC"}),s.brand==="Diners Club"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0069AA] text-white",children:e.jsx(Ce,{size:18})}),s.brand==="JCB"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0B4EA2] text-white",children:e.jsx(Pe,{size:18})}),s.brand==="UnionPay"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#00447C] text-sm font-bold text-white",children:"UP"})]}),e.jsxs("p",{className:"text-sm text-black sm:text-base",children:[s.brand," • ",s.last4]})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["Exp. ",s.exp_month,"/",s.exp_year.toString().slice(-2)]})})]})]},s.id)),e.jsxs("button",{onClick:()=>b(!0),className:"flex w-full items-center justify-center space-x-2 rounded-lg border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50 sm:w-auto",children:[e.jsx("span",{children:"+"}),e.jsx("span",{children:"Add card"})]})]}),e.jsx(ke,{isOpen:w,onClose:()=>b(!1),title:"Add card",primaryButtonText:"Add card",onPrimaryAction:B,submitting:S,showFooter:!1,children:e.jsx(Je,{onSubmit:B,getData:M,onClose:()=>b(!1)})})]})}let re=new K,Qe=new be;const X=g=>new Date(g*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});function We(){var r;const[g,v]=a.useState({}),[w,b]=a.useState([]),[S,_]=a.useState(10),[x,h]=a.useState(1),[m,P]=a.useState(1),[U,C]=a.useState(0),[G,I]=a.useState(!1),[A,p]=a.useState(!1),[y,j]=a.useState(!1),[T,f]=a.useState(!1),[F,B]=a.useState([]),[M,E]=a.useState(!1),[N,D]=a.useState({}),[s,l]=a.useState({}),[i,o]=a.useState(null),{triggerRefetch:R}=Ie(),O=fe(),{dispatch:Z}=a.useContext(H),{dispatch:V,state:Y}=$.useContext(ye);async function q(t,d,k={}){j(!0);try{console.log(Y);const W=i||parseInt(localStorage.getItem("user"));k.user_id=W;const ve=await re.getCustomerStripeSubscriptions({page:t,limit:d},k),{list:de,total:we,limit:Ne,num_pages:me,page:te}=ve,xe={};de.forEach(pe=>{pe.status==="active"&&(xe[pe.subId]=!0)}),v(xe),b(de),_(+Ne),h(+me),P(+te),C(+we),I(+te>1),p(+te+1<=+me)}catch(W){console.error(W),z(V,W.code)}finally{j(!1)}}const L=parseInt(localStorage.getItem("user"));async function Q(){try{const t=i||L,d=await re.getCustomerStripeSubscription(t);l(d.customer)}catch(t){console.error(t),z(V,t.code)}}const se=async t=>{E(!0);try{const d=await re.cancelStripeSubscription(t);if(d.error){console.error(d.message),u(Z,d.message,7500,"error");return}u(Z,d.message,1e4,"success"),q(1,S),f(!1),D({})}catch(d){console.error(d),u(Z,d.message,7500,"error"),z(V,d.code)}finally{E(!1)}},c=async()=>{try{const t=await Qe.getList("user",{filter:[`guardian,eq,${L}`,"role,cs,user"]});B(t.list)}catch(t){console.error("Error fetching family members:",t)}};a.useEffect(()=>{q(1,S),c()},[L]),a.useEffect(()=>{Q()},[L]),a.useEffect(()=>{i!==null&&(q(1,S),Q())},[i]);const n=t=>{o(t)};return console.log("currentTableData",w),e.jsxs("div",{className:"mx-auto max-w-3xl p-3 sm:p-6",children:[y&&e.jsx(ee,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Membership"}),e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row",children:[e.jsx(ue,{to:"/user/membership/buy",className:"w-full rounded-lg bg-gray-600 px-4 py-2 text-center text-white transition-colors hover:bg-gray-700 sm:w-auto",children:"Browse Plans"}),e.jsx("button",{onClick:()=>{const t=new URLSearchParams;if(i){t.set("familyMemberId",i);const d=F.find(k=>k.id===i);d&&t.set("familyMemberName",d.first_name)}O(`/user/membership/buy?${t.toString()}`)},className:"w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white transition-colors hover:bg-blue-700 sm:w-auto",children:i?`Buy plan for ${((r=F.find(t=>t.id===i))==null?void 0:r.first_name)||"family member"}`:"Buy new plan"})]})]}),e.jsx("div",{className:"mb-4 border-b border-gray-200"}),e.jsx("div",{className:"relative mb-4 flex justify-end",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"relative",children:e.jsxs("select",{value:i||L,onChange:t=>{const d=parseInt(t.target.value);d===L?o(null):n(d)},className:"w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsx("option",{value:L,children:"Myself"}),F.map(t=>e.jsxs("option",{value:t.id,children:[t.first_name," ",t.last_name," (",t.family_role||"Family Member",")"]},t.id))]})}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select the user whose membership you want to manage"})]})}),!y&&w.length===0&&e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-blue-100 p-3",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Active Memberships"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"You currently don't have any active membership plans."}),e.jsx(ue,{to:"/user/membership/buy",className:"rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700",children:"Browse Plans"})]}),w.map(t=>e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50",children:[e.jsxs("button",{onClick:()=>{v(d=>({...d,[t.subId]:!d[t.subId]}))},className:"flex w-full cursor-pointer flex-col gap-2 p-4 hover:bg-gray-100 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[g[t.subId]?e.jsx(Ee,{size:20}):e.jsx(Ae,{size:20}),e.jsxs("span",{className:"text-sm font-medium sm:text-base",children:[X(t.currentPeriodStart)," -"," ",X(t.currentPeriodEnd)]})]}),e.jsxs("div",{className:"flex items-center justify-between gap-3 pl-7 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600 sm:text-base",children:t.planName}),e.jsx("span",{className:`rounded-full px-3 py-1 text-sm capitalize ${t.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.status})]})]}),g[t.subId]&&e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6 rounded-xl border bg-white p-4",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row sm:items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Plan price"}),e.jsxs("div",{className:"text-left sm:text-right",children:[e.jsx("div",{className:"font-semibold",children:Te(t.planAmount)}),e.jsx("div",{className:"text-sm text-gray-500",children:"Billed annually"})]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Purchased on"}),e.jsx("span",{className:"text-sm sm:text-base",children:X(t.createdAt)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{className:"text-sm sm:text-base",children:X(t.currentPeriodEnd)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Subscription ID"}),e.jsx("span",{className:"break-all text-sm sm:text-base",children:t.subId})]}),t.status==="active"&&e.jsx("div",{className:"flex justify-center sm:justify-start",children:e.jsx("button",{onClick:()=>{f(!0),D(t)},className:"w-full rounded-xl bg-red-500 px-5 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto",children:"Cancel plan"})})]})})]},t.subId)),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${T?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-lg rounded-3xl bg-white p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#FDEDF0"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1 22.7V24.5H20.9V22.7H19.1ZM19.1 15.5V20.9H20.9V15.5H19.1Z",fill:"#DF1C41"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Are you sure?"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to cancel membership?"})]})]}),e.jsxs("div",{className:"flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{f(!1),D({})},className:"w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:"Go back"}),e.jsx(ce,{onClick:()=>{se(N.subId),R()},className:"w-full rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700 sm:w-auto",loading:M,children:"Yes, cancel"})]})]})]})]})}let Xe=new K;function es(){const{dispatch:g}=$.useContext(H),[v,w]=a.useState({}),[b,S]=a.useState([]);a.useState(0),a.useState(!1),a.useState(!1);const[_,x]=a.useState(""),[h,m]=a.useState(""),[P,U]=a.useState(""),[C,G]=a.useState("desc"),[I,A]=a.useState(!1),[p,y]=a.useState(null),j=a.useRef({}),T=s=>{w(l=>({...l,[s]:!l[s]}))},f=async(s={})=>{A(!0);try{let l=new URLSearchParams;s.sort&&l.append("sort",s.sort),s.invoice_type&&l.append("invoice_type",s.invoice_type),s.search&&l.append("search",s.search);const i=await Xe.callRawAPI(`/v3/api/custom/courtmatchup/user/billing/invoices${l.toString()?`?${l.toString()}`:""}`,{},"GET");if(i.error){u(g,i.message,5e3);return}S(i.invoices||[])}catch(l){console.error("ERROR",l),u(g,l.message,5e3),z(dispatch,l.message)}finally{A(!1)}},F=s=>{x(s.target.value),f({sort:C})},B=()=>!h&&!P?b:b.filter(s=>{try{const l=new Date(s.date);if(isNaN(l.getTime()))return!1;const i=h?new Date(h):null,o=P?new Date(P):null;return i&&o?l>=i&&l<=o:i?l>=i:o?l<=o:!0}catch{return console.error("Invalid date:",s.date),!1}}),M=()=>{const s=C==="asc"?"desc":"asc";G(s),f({sort:s})};a.useEffect(()=>{f({sort:C})},[]);const E=s=>new Date(s).toLocaleDateString(),N=s=>Number(s).toLocaleString("en-US",{style:"currency",currency:"usd"}),D=s=>{y(s);const l=window.open("","_blank");if(!l){u(g,"Please allow pop-ups to print receipts",5e3);return}if(!j.current[s]){u(g,"Receipt content not found",5e3);return}const o=b.find(R=>R.id===s);l.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt #${o.receipt_id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-id {
              font-size: 16px;
              color: #666;
            }
            .receipt-date {
              font-size: 14px;
              color: #666;
              margin-top: 5px;
            }
            .receipt-body {
              margin-bottom: 30px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 5px 0;
              border-bottom: 1px solid #f5f5f5;
            }
            .receipt-label {
              font-weight: 500;
              color: #666;
            }
            .receipt-total {
              margin-top: 20px;
              font-size: 18px;
              font-weight: bold;
              border-top: 2px solid #eee;
              padding-top: 10px;
            }
            .receipt-footer {
              margin-top: 40px;
              text-align: center;
              font-size: 14px;
              color: #999;
            }
            @media print {
              body {
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-id">Invoice #${o.receipt_id}</div>
            <div class="receipt-date">Date: ${E(o.date)}</div>
          </div>

          <div class="receipt-body">
            <div class="receipt-row">
              <span class="receipt-label">Type:</span>
              <span>${o.type}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Amount:</span>
              <span>${N(o.amount)}</span>
            </div>
            ${o.coach_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Coach Fee:</span>
              <span>${N(o.coach_fee)}</span>
            </div>
            `:""}
            ${o.service_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Service Fee:</span>
              <span>${N(o.service_fee)}</span>
            </div>
            `:""}
            ${o.club_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Club Fee:</span>
              <span>${N(o.club_fee)}</span>
            </div>
            `:""}
            <div class="receipt-row">
              <span class="receipt-label">Valid Until:</span>
              <span>${E(o.valid_until)}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Payment Method:</span>
              <span>${o.payment_method}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Status:</span>
              <span class="capitalize">${o.status}</span>
            </div>

            <div class="receipt-total">
              <div class="receipt-row">
                <span class="receipt-label">Total:</span>
                <span>${N(o.total_amount)}</span>
              </div>
            </div>
          </div>

          <div class="receipt-footer">
            Thank you for your payment.
          </div>

          <script>
            window.onload = function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `),l.document.close(),setTimeout(()=>{y(null)},1e3)};return e.jsxs("div",{className:"mx-auto max-w-3xl p-4 sm:p-6",children:[I&&e.jsx(ee,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Billing"}),e.jsxs("select",{className:"w-full rounded-lg border-gray-200 px-4 py-2 capitalize sm:w-auto",onChange:s=>f({invoice_type:s.target.value,sort:C}),children:[e.jsx("option",{value:"",children:"All bills"}),e.jsx("option",{value:"subscription",children:"Subscription"}),e.jsx("option",{value:"lesson",children:"Lesson"}),e.jsx("option",{value:"clinic",children:"Clinic"}),e.jsx("option",{value:"club_court",children:"Club Court"})]})]}),e.jsx("div",{className:"mb-5 border-b border-gray-200"}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Fe,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:_,onChange:F,placeholder:"Search by plan name or invoice number",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:h,onChange:s=>m(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:P,onChange:s=>U(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(h||P)&&e.jsx("button",{onClick:()=>{m(""),U("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:M,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:[C==="asc"?"Oldest first":"Latest first",e.jsx(Ge,{className:`transform ${C==="desc"?"rotate-180":""}`})]})]}),e.jsx("div",{className:"space-y-4",children:B().length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No billing records found"}):B().map(s=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>T(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Me,{className:`transform transition-transform ${v[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.type})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:E(s.create_at)}),e.jsx("span",{className:"font-medium",children:N(s.total_amount)})]})]}),v[s.id]&&e.jsx("div",{className:"mt-2 rounded-lg bg-white p-4",children:e.jsxs("div",{className:"space-y-4",ref:l=>j.current[s.id]=l,children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Amount"}),e.jsx("span",{children:N(s.amount)})]}),s.coach_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Coach fee"}),e.jsx("span",{children:N(s.coach_fee)})]}),s.service_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Service fee"}),e.jsx("span",{children:N(s.service_fee)})]}),s.club_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Club fee"}),e.jsx("span",{children:N(s.club_fee)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice ID"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Date"}),e.jsx("span",{children:E(s.date)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{children:E(s.valid_until)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Status"}),e.jsx("span",{className:"capitalize",children:s.status})]}),e.jsxs("button",{onClick:l=>{l.stopPropagation(),D(s.id)},className:"mt-4 flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50",children:[e.jsx($e,{className:"text-lg"}),"Print Receipt"]})]})})]},s.id))})]})}const qs=()=>{const{dispatch:g}=$.useContext(H),[v]=Se(),[w,b]=a.useState("profile"),S=fe();a.useEffect(()=>{const x=v.get("tab");x&&b({"payment-methods":"payment-methods",profile:"profile",membership:"membership",billing:"billing"}[x]||"profile")},[v.get("tab")]);const _=[{label:"Profile details",value:"profile",icon:Be},{label:"Payment methods",value:"payment-methods",icon:Ue},{label:"Membership",value:"membership",icon:ze},{label:"Billing",value:"billing",icon:De}];return a.useEffect(()=>{g({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(qe,{onBack:()=>S("/user/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:_.map(x=>{const h=x.icon;return e.jsxs("button",{onClick:()=>{b(x.value),S(`/user/profile?tab=${x.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${w===x.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(h,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:x.label})]},x.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[w==="profile"&&e.jsx(Ve,{}),w==="payment-methods"&&e.jsx(Ke,{}),w==="membership"&&e.jsx(We,{}),w==="billing"&&e.jsx(es,{})]})]})]})})};export{qs as default};
