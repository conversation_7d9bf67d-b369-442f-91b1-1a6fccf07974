import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as t,f as ae}from"./vendor-851db8c1.js";import{w as re,T as oe,M as ne,G as ie,A as ce,c as le,R as E,K as pe,t as de,b as ue}from"./index-3b44b02c.js";import"./index-be4468eb.js";import{c as me,a as g}from"./yup-54691517.js";import{u as he}from"./react-hook-form-687afde5.js";import{o as xe}from"./yup-2824f222.js";import"./AddButton.module-98aac587.js";import{P as fe}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ge from"./Skeleton-1e8bf077.js";import{C as be,E as ye}from"./EditClub-fe3061f6.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";let je=new oe,S=new ne;const D=[{header:"Name",accessor:"name"},{header:"Type",accessor:"type"},{header:"Revenue type",accessor:"revenue_type"},{header:"Country",accessor:"country"},{header:"State",accessor:"state"},{header:"Years under panel",accessor:"years_under_panel"},{header:"Contact person",accessor:"contact_person"},{header:"",accessor:"actions"}],ve=()=>{const{dispatch:N}=t.useContext(ie),{dispatch:F}=t.useContext(ce),[C,L]=t.useState([]),[i,w]=t.useState(10),[R,$]=t.useState(0),[Se,O]=t.useState(0),[l,B]=t.useState(1),[H,z]=t.useState(!1),[I,K]=t.useState(!1),[Ne,Y]=t.useState(!1);t.useState(!1),t.useState([]),t.useState([]),t.useState("eq");const[b,y]=t.useState(!0),[G,Ce]=t.useState(!1),[V,we]=t.useState(!1);t.useState(),ae();const M=t.useRef(null),[q,k]=t.useState(!1),[u,p]=t.useState(null),[Q,j]=t.useState(!1),[P,A]=t.useState(!1),[U,v]=t.useState(!1),J=me({id:g(),email:g(),role:g(),status:g()}),{register:T,handleSubmit:Me,formState:{errors:ke}}=he({resolver:xe(J)});function W(){o(l-1,i)}function X(){o(l+1,i)}async function o(s,r,a={},n=[]){y(!(V||G));try{const c=await je.getPaginate("clubs",{filter:n,page:s,limit:r,join:["user|user_id"]});c&&y(!1);const{list:h,total:x,limit:f,num_pages:m,page:d}=c;L(h),w(f),$(m),B(d),O(x),z(d>1),K(d+1<=m)}catch(c){y(!1),console.log("ERROR",c),de(F,c.message)}}const Z=s=>{s.target.value===""?o(l,i):o(l,i,{},[`${S._project_id}_user.status,cs,${s.target.value}`])};t.useEffect(()=>{N({type:"SETPATH",payload:{path:"dashboard"}});const r=setTimeout(async()=>{await o(1,20)},700);return()=>{clearTimeout(r)}},[]);const _=s=>{M.current&&!M.current.contains(s.target)&&Y(!1)};t.useEffect(()=>(document.addEventListener("mousedown",_),()=>{document.removeEventListener("mousedown",_)}),[]);const ee=s=>{const r=s.target.value;r?o(l,i,{},[`name,cs,${r.trim()}`]):o(l,i)},se=()=>{j(!0)},te=async()=>{A(!0),S.setTable("user"),(await S.callRestAPI({id:u.user.id,status:0},"PUT")).error||(ue(N,"Membership cancelled successfully",5e3,"success"),o(1,10),j(!1),p(r=>({...r,user:{...r.user,status:0}}))),A(!1)};return e.jsxs("div",{className:"h-screen px-8",children:[e.jsxs("div",{className:"flex flex-col items-start justify-between gap-4 py-3 md:flex-row lg:items-center",children:[e.jsxs("div",{className:"relative flex w-full max-w-[300px] flex-1 items-center gap-2",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex  items-center pl-3",children:e.jsx(le,{className:"text-gray-500"})}),e.jsx("input",{type:"text",className:"block w-full max-w-[300px] rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"search club",onChange:ee})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsxs("select",{className:"block w-[150px] rounded-lg border border-gray-200  py-2 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Type: Membership",onChange:Z,children:[e.jsx("option",{value:"",children:"All"}),e.jsx("option",{value:1,children:"Membership"}),e.jsx("option",{value:0,children:"Non-Membership"})]}),e.jsxs("select",{className:"block w-[150px] rounded-lg border border-gray-200  py-2 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Technology: Free",...T("date"),children:[e.jsx("option",{value:"",children:"Free"}),e.jsx("option",{value:"today"})]}),e.jsxs("select",{className:"block w-[150px] rounded-lg border border-gray-200  py-2 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Years under panel: All",...T("date"),children:[e.jsx("option",{value:"",children:"All"}),e.jsx("option",{value:"today"})]})]})]}),b?e.jsx(ge,{}):e.jsxs("div",{className:"overflow-x-auto ",children:[e.jsxs("table",{className:"w-full min-w-[1000px] table-auto",children:[e.jsx("thead",{children:e.jsx("tr",{children:D.map((s,r)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:s.header},r))})}),e.jsx("tbody",{className:"divide-y-8 divide-gray-50",children:C.map((s,r)=>e.jsx("tr",{className:"rounded-xl bg-gray-100 px-4 py-3 text-gray-500",children:D.map((a,n)=>{var c,h,x,f,m,d;return a.accessor=="actions"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>{k(!0),p(s)},className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M17.2083 14.7083V6.79167M17.2083 6.79167H9.29167M17.2083 6.79167L7 17",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{onClick:()=>{v(!0),p(s)},className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]})},n):a.mapping&&a.accessor==="status"?e.jsx("td",{className:"inline-block whitespace-nowrap px-6 py-5 text-sm",children:s[a.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:a.mapping[s[a.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:a.mapping[s[a.accessor]]})},n):a.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.mapping[s[a.accessor]]},n):a.accessor==="years_under_panel"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:Math.floor((new Date().getTime()-new Date(s==null?void 0:s.create_at).getTime())/(1e3*60*60*24*365.25))},n):a.accessor==="type"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((c=s==null?void 0:s.user)==null?void 0:c.status)===1?"Membership":"Non-Membership"},n):a.accessor==="contact_person"?e.jsxs("td",{className:"whitespace-nowrap px-6 py-4",children:[e.jsx("div",{children:!((h=s==null?void 0:s.user)!=null&&h.first_name)||!((x=s==null?void 0:s.user)!=null&&x.last_name)?"N/A":`${(f=s==null?void 0:s.user)==null?void 0:f.first_name} ${(m=s==null?void 0:s.user)==null?void 0:m.last_name}`}),e.jsx("div",{children:(d=s==null?void 0:s.user)==null?void 0:d.email})]},n):a.accessor==="players"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]||"N/A"},n):a.accessor==="revenue_type"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]||"N/A"},n):a.accessor==="country"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]||"N/A"},n):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[a.accessor]},n)})},r))})]}),b&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!b&&C.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(fe,{currentPage:l,pageCount:R,pageSize:i,canPreviousPage:H,canNextPage:I,updatePageSize:s=>{w(s),o(1,s)},previousPage:W,nextPage:X,gotoPage:s=>o(s,i)}),e.jsx(E,{isOpen:q,onClose:()=>{k(!1),p(null)},showFooter:!1,title:"Club Details",data:u,children:e.jsx(be,{data:u,setData:p,getData:o,onCancelMembership:se,membershipLoading:P})}),e.jsx(E,{isOpen:U,onClose:()=>{v(!1),p(null)},showFooter:!1,title:"Edit Club",data:u,children:e.jsx(ye,{data:u,setData:p,getData:o,onClose:()=>v(!1)})}),e.jsx(pe,{isOpen:Q,onClose:()=>j(!1),onDelete:te,title:"Cancel Membership",message:"Are you sure you want to cancel the membership?",loading:P,buttonText:"Cancel Membership"})]})},fs=re(ve,"dashboard","You don't have permission to access the dashboard");export{fs as default};
