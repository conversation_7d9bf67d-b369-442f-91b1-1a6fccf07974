import{j as a}from"./@nivo/heatmap-ba1ecfff.js";import{b as e,f as K}from"./vendor-851db8c1.js";import{M as w,A as J,G as y,t as Q,b as U}from"./index-3b44b02c.js";import{o as V}from"./yup-2824f222.js";import{c as W,a as l}from"./yup-54691517.js";import{u as X}from"./react-hook-form-687afde5.js";import{P as Y}from"./index-eb1bc208.js";import{M as Z}from"./index-be4468eb.js";import tt from"./AddAdminPhotoPage-366a619f.js";import{A as et}from"./AddButton-df0c3574.js";import{S as at}from"./index-02625b16.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./@uppy/xhr-upload-ffe6b130.js";import"./@uppy/aws-s3-multipart-9d71a8f2.js";import"./@uppy/react-588f1389.js";import"./@uppy/drag-drop-9b58d36f.js";import"./@uppy/progress-bar-4d089812.js";import"./@uppy/file-input-630decca.js";import"./AddButton.module-98aac587.js";let c=new w;const P=[{header:"Photos",accessor:"url"},{header:"Create at",accessor:"create_at"},{header:"Action",accessor:""}],te=()=>{const{dispatch:b}=e.useContext(J),{dispatch:u}=e.useContext(y);e.useState("");const[v,A]=e.useState([]),[p,h]=e.useState(3),[f,N]=e.useState(0),[st,C]=e.useState(0),[o,E]=e.useState(0),[k,D]=e.useState(!1),[T,R]=e.useState(!1),[it,x]=e.useState(!1),[L,m]=e.useState(!1),[ot,M]=e.useState(!1);e.useState(!1),e.useState([]),e.useState([]),e.useState(""),e.useState("eq"),K(),e.useContext(y);const g=e.useRef(null),[F,I]=e.useState(!0),z=W({date:l(),id:l(),user_id:l()});X({resolver:V(z)});function G(t){(async function(){h(t),await r(0,t)})()}function O(){(async function(){await r(o-1>0?o-1:0,p)})()}function _(){(async function(){await r(o+1<=f?o+1:0,p)})()}async function r(t,n,s){try{c.setTable("photo");const i=await c.callRestAPI({payload:{...s},page:t,limit:n},"PAGINATE"),{list:B,total:q,limit:H,num_pages:j,page:d}=i;A(B),h(H),N(j),E(d),C(q),D(d>1),R(d+1<=j),I(!1)}catch(i){console.log("ERROR",i),Q(b,i.message)}}e.useEffect(()=>{u({type:"SETPATH",payload:{path:"photo"}}),async function(){x(!0),await r(0,50),x(!1)}()},[]);async function $(t){c.setTable("photo"),await c.callRestAPI({id:t},"DELETE"),U(u,"Deleted"),await r(0,50)}const S=t=>{g.current&&!g.current.contains(t.target)&&M(!1)};return e.useEffect(()=>(document.addEventListener("mousedown",S),()=>{document.removeEventListener("mousedown",S)}),[]),a.jsxs("div",{className:"px-8",children:[a.jsx("div",{className:"flex items-center justify-between py-3",children:a.jsx(et,{onClick:()=>m(!0)})}),a.jsx("div",{children:F?a.jsx(at,{}):a.jsx("div",{className:"shadow overflow-x-auto border-b border-gray-200 ",children:a.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[a.jsx("thead",{className:"bg-gray-50",children:a.jsx("tr",{children:P.map((t,n)=>a.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:[t.header,a.jsx("span",{children:t.isSorted?t.isSortedDesc?" ▼":" ▲":""})]},n))})}),a.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:v.map((t,n)=>a.jsx("tr",{children:P.map((s,i)=>s.accessor==""?a.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:a.jsxs("button",{className:"text-xs text-red-400",onClick:()=>{$(t.id)},children:[" ","Delete"]})},i):s.mapping?a.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s.mapping[t[s.accessor]]},i):a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.accessor=="url"?a.jsx("img",{width:200,height:200,src:t!=null&&t.url.includes("http")?t==null?void 0:t.url:`https://mkdlabs.com${t==null?void 0:t.url}`,alt:t!=null&&t.caption?t==null?void 0:t.caption:"broken image link"}):t[s.accessor]},i))},n))})]})})}),a.jsx(Y,{currentPage:o,pageCount:f,pageSize:p,canPreviousPage:k,canNextPage:T,updatePageSize:G,previousPage:O,nextPage:_}),a.jsx(Z,{isModalActive:L,closeModalFn:()=>m(!1),children:a.jsx(tt,{setSidebar:m})})]})};export{te as default};
