import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{L as o}from"./ListStaff-a75d328b.js";import{u as i}from"./index-3b44b02c.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./index-be4468eb.js";import"./cal-heatmap-cf010ec4.js";import"./yup-54691517.js";import"./index.esm-9c6194ba.js";import"./react-icons-51bc3cff.js";import"./AddButton.module-98aac587.js";import"./index-eb1bc208.js";import"./lodash-91d5d207.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./numeral-ea653b2a.js";import"./TimeslotPicker-06293178.js";import"./BottomDrawer-f0d615b3.js";import"./TimeSlotGrid-3140c36d.js";import"./InvitationCard-d74f79df.js";import"./RoleAccessManager-6c63c7e7.js";import"./DataTable-a2248415.js";import"./HistoryComponent-05a07961.js";import"./date-fns-07266b7d.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";function _(){const{club:t}=i();return t?r.jsx(r.Fragment,{children:r.jsx(o,{club:t,sports:t==null?void 0:t.sports})}):r.jsx("div",{children:"No club selected"})}export{_ as default};
