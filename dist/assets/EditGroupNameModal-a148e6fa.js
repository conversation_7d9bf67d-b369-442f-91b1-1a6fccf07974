import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as d,b as I}from"./vendor-851db8c1.js";import{a as D,b as ne,c as ie,d as oe,e as de,f as ce,g as ee,h as se,i as me,j as V,k as ue,l as xe,m as H,n as pe,o as he}from"./index.esm-09a3a6b8.js";import{u as fe,C as G}from"./react-hook-form-687afde5.js";import{o as ge}from"./yup-2824f222.js";import{c as be,a as k,b as ye}from"./yup-54691517.js";import{G as R,d as L,M as O,b as U,u as je,am as ve,R as Ne,e as we,E as J,J as W,H as Ce}from"./index-3b44b02c.js";import{P as te}from"./react-phone-input-2-57d1f0dd.js";import{C as _e,S as ke,a as Se}from"./country-state-city-282f4569.js";/* empty css              */import"./react-tooltip-7a26650a.js";const Be=({group:p,currentUserId:c,onAddMember:s,onEditName:g,onDeleteGroup:u,onViewProfile:i,onRemoveMember:y,onAddExistingUser:m,onAddFamilyMember:v,onInviteToCourtmatch:r,sentInvites:j=[]})=>{j.filter(t=>t.status==="pending"&&t.group_id===p.group_id);const[h,w]=d.useState(!1),[S,b]=d.useState(null),[M,n]=d.useState(!1),[N,C]=d.useState({}),_=d.useRef(null),B=d.useRef(null),o=d.useRef(null),P=d.useRef(null),l=localStorage.getItem("user"),a=p.group_owner_id&&parseInt(p.group_owner_id)===parseInt(c||l);d.useEffect(()=>{const t=f=>{B.current&&!B.current.contains(f.target)&&(w(!1),n(!1)),o.current&&!o.current.contains(f.target)&&b(null),P.current&&!P.current.contains(f.target)&&n(!1)};return document.addEventListener("mousedown",t),()=>document.removeEventListener("mousedown",t)},[]);const x=p.members.filter(t=>!(t.guardian&&t.guardian!=l));return e.jsxs("div",{className:"h-fit max-h-fit rounded-xl border border-gray-100 bg-white p-6 shadow-sm transition-shadow hover:shadow-md",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600",children:e.jsx(D,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:p.group_name}),e.jsxs("p",{className:"text-sm text-gray-500",children:[x.length," member",x.length!==1?"s":""]})]})]}),a&&e.jsxs("div",{className:"relative flex items-center gap-2",children:[e.jsx("button",{onClick:()=>w(!h),className:"rounded p-1 hover:bg-gray-100",children:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",fill:"#868C98"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",fill:"#868C98"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",fill:"#868C98"}),e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),h&&e.jsx("div",{ref:B,className:"absolute right-0 z-10 mt-2 w-48 rounded-xl border-2 border-gray-200 bg-white",children:e.jsxs("div",{className:"py-1",children:[a&&e.jsxs("div",{className:"relative",children:[e.jsxs("button",{onClick:()=>n(!M),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(ne,{className:"mr-2"})," Add member",e.jsx("button",{className:"ml-auto flex items-center",children:e.jsx(ie,{className:"text-lg text-gray-500"})})]}),M&&e.jsx("div",{ref:P,className:"absolute left-0 top-0 z-20 ml-2 w-52 translate-x-[-105%] rounded-xl border-2 border-gray-200 bg-white sm:left-full sm:translate-x-0",children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>{m(p.id),n(!1),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(D,{className:"mr-2"})," Add existing user"]}),e.jsxs("button",{onClick:()=>{v(p.id),n(!1),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(D,{className:"mr-2"})," Add family member"]}),e.jsxs("button",{onClick:()=>{r(p.id),n(!1),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(oe,{className:"mr-2"})," Invite to CourtMatch"]})]})})]}),a&&e.jsxs("button",{onClick:()=>{g(p.id),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(de,{className:"mr-2"})," Edit name"]}),a&&e.jsxs("button",{onClick:()=>{u(p.id),w(!1)},className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx(ce,{className:"mr-2"})," Delete group"]})]})})]})]}),e.jsx("div",{ref:_,className:"max-h-[350px] space-y-3 overflow-y-auto rounded-xl border border-gray-200 bg-gray-50/50 p-4",children:x.map(t=>{const f=t.guardian==l;return e.jsxs("div",{className:`group flex items-center justify-between rounded-lg p-3 transition-colors hover:bg-white ${f?"border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50":"border border-gray-100 bg-white"}`,children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full bg-gray-200 shadow-sm ring-2 ring-white",children:t.photo?e.jsx("img",{src:t.photo,alt:`${t.first_name} ${t.last_name}`,className:"h-full w-full object-cover"}):e.jsx("div",{className:"flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200",children:e.jsx(ee,{className:"h-6 w-6 text-gray-400"})})}),f&&e.jsx("div",{className:"absolute -bottom-1 -right-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 ring-2 ring-white",children:e.jsx(se,{className:"h-3 w-3 text-white"})})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:[t.first_name," ",t.last_name]}),f&&e.jsxs("span",{className:"inline-flex items-center gap-1 rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-700",children:[e.jsx(se,{className:"h-3 w-3"}),"Family"]})]}),t.family_role&&e.jsx("span",{className:"text-xs font-medium capitalize text-blue-600",children:t.family_role}),(!f||!t.family_role)&&t.email&&e.jsx("span",{className:"text-xs text-gray-500",children:t.email})]})]}),e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:F=>{if(S===t.id){b(null);return}const E=F.currentTarget.getBoundingClientRect(),Y=_.current.getBoundingClientRect(),K=_.current.scrollTop,re=E.top-Y.top+K+E.height,$=Y.height-(re-K),q=100,X=$<q?"top":"bottom";if(C(z=>({...z,[t.id]:X})),X==="bottom"&&$<q){const z=q-$+10;_.current.scrollTop+=z}b(t.id)},className:"invisible rounded p-1 hover:bg-gray-100 group-hover:visible",children:e.jsxs("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",fill:"#868C98"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",fill:"#868C98"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",fill:"#868C98"}),e.jsx("path",{d:"M12.3333 13C12.8856 13 13.3333 12.5523 13.3333 12C13.3333 11.4477 12.8856 11 12.3333 11C11.7811 11 11.3333 11.4477 11.3333 12C11.3333 12.5523 11.7811 13 12.3333 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M20.5833 13C21.1356 13 21.5833 12.5523 21.5833 12C21.5833 11.4477 21.1356 11 20.5833 11C20.0311 11 19.5833 11.4477 19.5833 12C19.5833 12.5523 20.0311 13 20.5833 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M4.08334 13C4.63563 13 5.08334 12.5523 5.08334 12C5.08334 11.4477 4.63563 11 4.08334 11C3.53106 11 3.08334 11.4477 3.08334 12C3.08334 12.5523 3.53106 13 4.08334 13Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),S===t.id&&e.jsx("div",{ref:o,className:`absolute right-0 z-50 w-48 rounded-xl border-2 border-gray-200 bg-white ${N[t.id]==="top"?"bottom-full mb-2":"top-full mt-2"}`,children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>{i(t.id),b(null)},className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx(ee,{className:"mr-2"})," View profile"]}),a&&e.jsxs("button",{onClick:()=>{y(p,t),b(null)},className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx(me,{className:"mr-2"})," Remove from group"]})]})})]})]},t.id)})})]})},De=Be;function Z({options:p,value:c,onChange:s,placeholder:g,label:u,disabled:i=!1,className:y=""}){const[m,v]=d.useState(!1),[r,j]=d.useState(""),[h,w]=d.useState([]),S=d.useRef(null);d.useEffect(()=>{if(r.trim()==="")w(p.slice(0,10));else{const n=p.filter(N=>N.label.toLowerCase().includes(r.toLowerCase())).slice(0,10);w(n)}},[r,p]),d.useEffect(()=>{const n=p.find(N=>N.value===c);j(n?n.label:"")},[c,p]),d.useEffect(()=>{function n(N){S.current&&!S.current.contains(N.target)&&v(!1)}return document.addEventListener("mousedown",n),()=>document.removeEventListener("mousedown",n)},[S]);const b=n=>{j(n.target.value),v(!0),n.target.value||s({value:"",label:""})},M=n=>{j(n.label),s(n),v(!1)};return e.jsxs("div",{className:"relative",ref:S,children:[u&&e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:u}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(V,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{type:"text",value:r,onChange:b,onFocus:()=>v(!0),placeholder:g,disabled:i,className:`block w-full rounded-xl border border-gray-300 py-2 pl-10 pr-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue ${y}`})]}),m&&h.length>0&&e.jsx("div",{className:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5",children:h.map(n=>e.jsx("div",{onClick:()=>M(n),className:"cursor-pointer px-4 py-2 hover:bg-gray-100",children:n.label},n.value))})]})}let ae=new O;const Ue=be().shape({firstName:k().required("First name is required"),lastName:k().required("Last name is required"),gender:k(),familyRole:k().required("Family role is required"),email:k().email("Invalid email format"),allowLogin:ye(),password:k().when("allowLogin",{is:!0,then:()=>k().required("Password is required").min(6,"Password must be at least 6 characters"),otherwise:()=>k()}),phoneNumber:k().required("Phone number is required"),dateOfBirth:k().required("Date of birth is required"),address:k().required("Address is required"),country:k().required("Country is required"),city:k().required("City is required"),state:k().required("State is required"),zipCode:k().required("Zip code is required"),alternatePhone:k(),ageGroup:k().required("Age group is required"),additionalInfo:k()});function Ze({onSubmit:p,onClose:c,user:s,group:g,fetchData:u}){const[i,y]=d.useState(!1),[m,v]=d.useState(!1),[r,j]=d.useState(!1),{dispatch:h}=d.useContext(R),[w,S]=d.useState([]),[b,M]=d.useState([]),{register:n,handleSubmit:N,control:C,watch:_,setValue:B,formState:{errors:o}}=fe({resolver:ge(Ue),defaultValues:{firstName:"",lastName:"",gender:"",familyRole:"",email:"",username:"",password:"",phoneNumber:"",dateOfBirth:"",address:"",country:"",city:"",state:"",zipCode:"",alternatePhone:"",ageGroup:"",additionalInfo:"",allowLogin:!1}}),P=_("country"),l=_("state"),a=d.useMemo(()=>_e.getAllCountries().map(t=>({value:t.isoCode,label:t.name})),[]);d.useEffect(()=>{if(P){const t=ke.getStatesOfCountry(P).map(f=>({value:f.isoCode,label:f.name}));S(t),B("state",""),B("city","")}},[P,B]),d.useEffect(()=>{if(P&&l){const t=Se.getCitiesOfState(P,l).map(f=>({value:f.name,label:f.name}));M(t)}},[P,l]),console.log("errors",o);const x=async t=>{if(g&&g.group_owner_id&&parseInt(g.group_owner_id)!==parseInt(s==null?void 0:s.id)){U(h,"Only the group owner can add family members",3e3,"error");return}j(!0),console.log(t);const f={email:t.email||"",password:t.password||"",role:"user",verify:!0,is_refresh:!1,first_name:t.firstName,last_name:t.lastName,photo:"",password_login:t.allowLogin?1:0,phone:t.phoneNumber,alternative_phone:t.alternatePhone,age_group:t.ageGroup,another:"",family_role:t.familyRole,address:t.address,city:t.city,state:t.state,country:t.country,zip_code:t.zipCode,house_no:"",date_of_birth:t.dateOfBirth,club_id:s.club_id,guardian:s.id};try{const F=await ae.callRawAPI("/v3/api/custom/courtmatchup/users/register",f,"POST");console.log(F);const E=await ae.callRawAPI(`/v3/api/custom/courtmatchup/user/groups/add/${g.group_id}`,{members:[F.user_id]},"POST");U(h,"Family member created successfully",3e3,"success"),await u(),c()}catch(F){U(h,F==null?void 0:F.message,3e3,"error"),console.log(F)}finally{j(!1)}};return e.jsxs("form",{onSubmit:N(x),children:[e.jsxs("div",{className:"space-y-4 py-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"First name"}),e.jsx("input",{type:"text",...n("firstName"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.firstName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.firstName.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Last name"}),e.jsx("input",{type:"text",...n("lastName"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.lastName&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.lastName.message})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Gender ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsxs("select",{...n("gender"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue",children:[e.jsx("option",{value:"",children:"- select -"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"}),e.jsx("option",{value:"other",children:"Other"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Family role"}),e.jsxs("select",{...n("familyRole"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue",children:[e.jsx("option",{value:"",children:"- select -"}),e.jsx("option",{value:"Parent",children:"Parent"}),e.jsx("option",{value:"Child",children:"Child"}),e.jsx("option",{value:"Sibling",children:"Sibling"}),e.jsx("option",{value:"Grandparent",children:"Grandparent"}),e.jsx("option",{value:"Spouse",children:"Spouse"}),e.jsx("option",{value:"Aunt",children:"Aunt"}),e.jsx("option",{value:"Uncle",children:"Uncle"}),e.jsx("option",{value:"Cousin",children:"Cousin"}),e.jsx("option",{value:"Guardian",children:"Guardian"}),e.jsx("option",{value:"Step-parent",children:"Step-parent"}),e.jsx("option",{value:"Step-sibling",children:"Step-sibling"}),e.jsx("option",{value:"In-law",children:"In-law"}),e.jsx("option",{value:"Godparent",children:"Godparent"})]}),o.familyRole&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.familyRole.message})]}),!m&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Email ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("input",{type:"email",...n("email"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.email.message})]}),e.jsxs("div",{className:"space-y-4 rounded-xl bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",id:"allowLogin",...n("allowLogin"),onChange:t=>{B("allowLogin",t.target.checked),v(t.target.checked)},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("label",{htmlFor:"allowLogin",className:"text-sm font-medium text-gray-700",children:"Allow log in"})]}),m&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"relative",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"email",...n("email"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.email&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.email.message})]}),e.jsxs("div",{className:"relative",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Password"}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx("input",{type:i?"text":"password",...n("password"),className:"block w-full rounded-xl border border-gray-300 px-3 py-2 pr-10 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),e.jsx("button",{type:"button",onClick:()=>y(!i),className:"absolute inset-y-0 right-0 flex items-center pr-3",children:i?e.jsx(ue,{className:"h-5 w-5 text-gray-400"}):e.jsx(xe,{className:"h-5 w-5 text-gray-400"})})]}),o.password&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.password.message})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Phone number"}),e.jsx("div",{className:"mt-1 flex",children:e.jsx(G,{name:"phoneNumber",control:C,render:({field:{onChange:t,value:f}})=>e.jsx(te,{country:"us",value:f,onChange:t,containerClass:"mt-1",inputClass:"!w-full !h-[42px] !rounded-xl !border-zinc-200",buttonClass:"!border-zinc-200 !rounded-l-xl",placeholder:"(*************"})})}),o.phoneNumber&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.phoneNumber.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Date of birth"}),e.jsx("input",{type:"date",...n("dateOfBirth"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.dateOfBirth&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.dateOfBirth.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Address"}),e.jsx("input",{type:"text",...n("address"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.address&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.address.message})]}),e.jsxs("div",{children:[e.jsx(G,{name:"country",control:C,render:({field:t})=>e.jsx(Z,{label:"Country",options:a,value:t.value,onChange:f=>t.onChange(f.value),placeholder:"Search country..."})}),o.country&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.country.message})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(G,{name:"state",control:C,render:({field:t})=>e.jsx(Z,{label:"State",options:w,value:t.value,onChange:f=>t.onChange(f.value),placeholder:"Search state...",disabled:!P})}),o.state&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.state.message})]}),e.jsxs("div",{children:[e.jsx(G,{name:"city",control:C,render:({field:t})=>e.jsx(Z,{label:"City",options:b,value:t.value,onChange:f=>t.onChange(f.value),placeholder:"Search city...",disabled:!l})}),o.city&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.city.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Zip code"}),e.jsx("input",{type:"text",...n("zipCode"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"}),o.zipCode&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.zipCode.message})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Alternate phone number"," ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("div",{className:"mt-1 flex",children:e.jsx(G,{name:"alternatePhone",control:C,render:({field:{onChange:t,value:f}})=>e.jsx(te,{country:"us",value:f,onChange:t,containerClass:"mt-1",inputClass:"!w-full !h-[42px] !rounded-xl !border-zinc-200",buttonClass:"!border-zinc-200 !rounded-l-xl",placeholder:"(*************"})})}),o.alternatePhone&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.alternatePhone.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Age group"}),e.jsxs("select",{...n("ageGroup"),className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue",children:[e.jsx("option",{value:"",children:"- select -"}),e.jsx("option",{value:"toddler",children:"3-5 years (Toddler)"}),e.jsx("option",{value:"child",children:"6-12 years (Child)"}),e.jsx("option",{value:"teen",children:"13-17 years (Teen)"}),e.jsx("option",{value:"young_adult",children:"18-25 years (Young Adult)"}),e.jsx("option",{value:"adult",children:"26-39 years (Adult)"}),e.jsx("option",{value:"middle_aged",children:"40-59 years (Middle-aged)"}),e.jsx("option",{value:"senior",children:"60+ years (Senior)"})]}),o.ageGroup&&e.jsx("p",{className:"mt-1 text-sm text-red-600",children:o.ageGroup.message})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Additional info ",e.jsx("span",{className:"text-gray-500",children:"(Optional)"})]}),e.jsx("textarea",{...n("additionalInfo"),rows:3,className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"})]})]}),e.jsxs("div",{className:"sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:c,children:"Cancel"}),e.jsx(L,{type:"submit",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",loading:r,children:r?"Saving...":"Add family member"})]})]})}let Pe=new O;function Qe({users:p,onClose:c,fetchData:s}){const{dispatch:g}=I.useContext(R),{user_subscription:u,user_permissions:i}=je(),[y,m]=d.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),[v,r]=d.useState({groupName:"",isFamilyGroup:!1,selectedUsers:[]}),[j,h]=d.useState({}),[w,S]=d.useState(""),[b,M]=d.useState(!1),n=a=>{const{name:x,value:t,type:f,checked:F}=a.target;r(E=>({...E,[x]:f==="checkbox"?F:t})),j[x]&&h(E=>({...E,[x]:""}))},N=a=>{r(x=>({...x,selectedUsers:x.selectedUsers.includes(a)?x.selectedUsers.filter(t=>t!==a):[...x.selectedUsers,a]})),j.selectedUsers&&h(x=>({...x,selectedUsers:""}))},C=a=>{r(x=>({...x,selectedUsers:x.selectedUsers.filter(t=>t!==a)}))},_=a=>`${a.first_name} ${a.last_name}`,B=p.filter(a=>_(a).toLowerCase().includes(w.toLowerCase())),o=p.filter(a=>v.selectedUsers.includes(a.id)),P=()=>{const a={};return v.groupName.trim()||(a.groupName="Group name is required"),v.selectedUsers.length===0&&(a.selectedUsers="Please select at least one member"),h(a),Object.keys(a).length===0},l=async()=>{if(!(u!=null&&u.planId)){m({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to create groups",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(i!=null&&i.allowGroups)){m({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${i==null?void 0:i.planName}) does not include group creation. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(P()){M(!0);try{const a={name:v.groupName.trim(),members:v.selectedUsers,type:v.isFamilyGroup?1:0};await Pe.callRawAPI("/v3/api/custom/courtmatchup/user/groups/create",a,"POST")&&(c(),U(g,"Group created successfully",3e3,"success"),s())}catch(a){console.error("Error creating group:",a),m({isOpen:!0,title:"Group Creation Error",message:(a==null?void 0:a.message)||"Error creating group",type:"error"})}finally{M(!1)}}};return console.log({selectedUsersList:o}),e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx(ve,{isOpen:y.isOpen,onClose:()=>m({...y,isOpen:!1}),title:y.title,message:y.message,actionButtonText:y.actionButtonText,actionButtonLink:y.actionButtonLink,type:y.type}),e.jsxs("div",{className:"flex-1 overflow-y-auto",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block font-medium text-gray-700",children:"Group name"}),e.jsx("input",{type:"text",name:"groupName",value:v.groupName,onChange:n,className:`mt-1 block w-full rounded-md border ${j.groupName?"border-red-500":"border-gray-300"} px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue`}),j.groupName&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.groupName})]}),e.jsx("div",{className:"mb-6 mt-6 text-gray-600",children:"An invite will be sent out to the users you select via email. Once they accept, they would successfully be added to the group."}),e.jsxs("div",{className:"space-y-4",children:[o.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 rounded-lg bg-gray-50 p-2",children:o.map(a=>e.jsxs("div",{className:"flex items-center space-x-1 rounded-full bg-white px-2 py-1 shadow-sm",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full",children:e.jsx("img",{src:(a==null?void 0:a.photo)||"/default-avatar.png",alt:_(a),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:_(a)}),e.jsx("button",{type:"button",onClick:()=>C(a.id),className:"ml-1 rounded-full p-0.5 hover:bg-gray-100",children:e.jsx(H,{className:"h-4 w-4 text-gray-500"})})]},a.id))}),e.jsxs("div",{className:"relative",children:[e.jsx(V,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"search by name",value:w,onChange:a=>S(a.target.value),className:"block w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"})]}),e.jsx("div",{className:`max-h-96 overflow-y-auto rounded-xl bg-gray-50 p-2 ${j.selectedUsers?"border-2 border-red-500":""}`,children:B.map(a=>e.jsxs("div",{className:"flex items-center space-x-3 py-2",children:[e.jsx("input",{type:"checkbox",checked:v.selectedUsers.includes(a.id),onChange:()=>N(a.id),className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full",children:e.jsx("img",{src:(a==null?void 0:a.photo)||"/default-avatar.png",alt:_(a),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:_(a)})]},a.id))}),j.selectedUsers&&e.jsx("p",{className:"text-sm text-red-500",children:j.selectedUsers})]})]}),e.jsxs("div",{className:"sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:c,children:"Cancel"}),e.jsx(L,{type:"button",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:l,loading:b,children:b?"Saving...":"Create group"})]})]})}const A=I.memo(({label:p,field:c,value:s,onChange:g,isEditMode:u,editLoading:i,user:y})=>e.jsx("div",{className:"mb-4",children:e.jsxs("div",{children:[e.jsx("label",{htmlFor:c,className:"text-sm uppercase text-gray-500",children:p}),u?e.jsx("input",{type:"text",id:c,name:c,value:s,onChange:g,className:"mt-1 w-full rounded-xl border border-gray-300 p-2 focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",disabled:i,autoComplete:"off"}):e.jsx("div",{className:"text-gray-900",children:y[c]||"Not set"})]})}));A.displayName="ProfileField";const Me=({isOpen:p,onClose:c,user:s,fetchData:g})=>{var M,n;if(!s)return null;const{dispatch:u}=I.useContext(R),[i,y]=I.useState(!1),[m,v]=I.useState(!1),[r,j]=I.useState({}),h=I.useRef(null);I.useEffect(()=>{s&&j({first_name:s.first_name||"",last_name:s.last_name||"",gender:s.gender||"",family_role:s.family_role||"",email:s.email||"",phone:s.phone||"",date_of_birth:s.date_of_birth||"",address:s.address||"",city:s.city||"",state:s.state||"",zip_code:s.zip_code||"",alternative_phone:s.alternative_phone||"",age_group:s.age_group||"",bio:s.bio||""})},[s]);const w=async N=>{N==null||N.preventDefault();try{y(!0);const C=new O,_={first_name:r.first_name,last_name:r.last_name,family_role:r.family_role,email:r.email,phone:r.phone,age_group:r.age_group,id:s.id};C.setTable("user"),(await C.callRestAPI(_,"PUT")).error||(U(u,"All changes saved successfully",3e3,"success"),v(!1),typeof c=="function"&&(c(),g()))}catch(C){U(u,C==null?void 0:C.message,3e3,"error")}finally{y(!1)}},S=N=>{N==null||N.preventDefault(),v(!1),j({first_name:s.first_name||"",last_name:s.last_name||"",gender:s.gender||"",family_role:s.family_role||"",email:s.email||"",phone:s.phone||"",date_of_birth:s.date_of_birth||"",address:s.address||"",city:s.city||"",state:s.state||"",zip_code:s.zip_code||"",alternative_phone:s.alternative_phone||"",age_group:s.age_group||"",bio:s.bio||""})},b=I.useCallback(N=>{const{name:C,value:_}=N.target;j(B=>({...B,[C]:_}))},[]);return e.jsxs(Ne,{isOpen:p,onClose:c,title:"Profile details",showFooter:!1,children:[i&&e.jsx(we,{}),e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{className:"text-sm uppercase text-gray-500",children:"MEMBER"}),m?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{type:"button",onClick:S,className:"flex items-center gap-1 rounded-xl border border-gray-300 px-4 py-2 text-sm text-gray-600 hover:bg-gray-50",disabled:i,children:[e.jsx(H,{className:"h-4 w-4"}),"Cancel"]}),e.jsxs("button",{type:"submit",form:"profile-form",className:"flex items-center gap-1 rounded-xl bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700 disabled:opacity-50",disabled:i,children:[e.jsx(pe,{className:"h-4 w-4"}),i?"Saving...":"Save All"]})]}):e.jsxs("button",{type:"button",onClick:()=>v(!0),className:"flex items-center gap-1 rounded-xl bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700",children:[e.jsx(he,{className:"h-4 w-4"}),"Edit Profile"]})]}),e.jsxs("div",{className:"mb-6 flex items-center space-x-3",children:[e.jsx("div",{className:"h-12 w-12 overflow-hidden rounded-full bg-gray-200",children:s.photo?e.jsx("img",{src:s.photo,alt:`${s.first_name} ${s.last_name}`,className:"h-full w-full object-cover"}):e.jsxs("div",{className:"flex h-full w-full items-center justify-center bg-primaryBlue text-lg text-white",children:[(M=s.first_name)==null?void 0:M.charAt(0),(n=s.last_name)==null?void 0:n.charAt(0)]})}),e.jsxs("h2",{className:"text-xl font-medium",children:[s.first_name," ",s.last_name]})]}),e.jsx("div",{className:"mb-4 text-sm uppercase text-gray-500",children:"DETAILS"}),e.jsxs("form",{id:"profile-form",ref:h,onSubmit:w,className:"space-y-4",children:[e.jsx(A,{label:"First Name",field:"first_name",value:r.first_name,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Last Name",field:"last_name",value:r.last_name,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Gender",field:"gender",value:r.gender,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Family Role",field:"family_role",value:r.family_role,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Email",field:"email",value:r.email,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Phone Number",field:"phone",value:r.phone,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Date of Birth",field:"date_of_birth",value:r.date_of_birth,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Address",field:"address",value:r.address,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"City",field:"city",value:r.city,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"State",field:"state",value:r.state,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Zip Code",field:"zip_code",value:r.zip_code,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Alternate Phone Number",field:"alternative_phone",value:r.alternative_phone,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsx(A,{label:"Age Group",field:"age_group",value:r.age_group,onChange:b,isEditMode:m,editLoading:i,user:s}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"bio",className:"mb-2 text-sm uppercase text-gray-500",children:"Additional Info"}),e.jsx("div",{children:m?e.jsx("textarea",{id:"bio",name:"bio",value:r.bio||"",onChange:b,className:"mt-1 w-full rounded-xl border border-gray-300 p-2 focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",disabled:i,rows:4}):e.jsx("div",{className:"text-sm text-gray-900",children:s.bio||"Not set"})})]})]})]})]})},Ve=Me;let Q=new O;function He({title:p,onClose:c,message:s,group:g,user:u}){const[i,y]=d.useState(""),{dispatch:m}=d.useContext(R),[v,r]=d.useState(!1),j=async()=>{try{r(!0);const h=await Q.callRawAPI(`/v3/api/custom/courtmatchup/user/groups/invite/${g.id}`,{email:i},"POST");Q.setTable("activity_logs"),await Q.callRestAPI({user_id:u==null?void 0:u.id,action:"Invited user to group",activity_type:J.group,action_type:W.CREATE,data:JSON.stringify({group:g}),club_id:u==null?void 0:u.club_id,description:"Invited user to group"},"POST"),h.error?U(m,"Failed to send invitation",3e3,"error"):(U(m,"Invitation sent successfully"),c())}catch(h){U(m,h.message,3e3,"error")}finally{r(!1)}};return e.jsx("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"contact-info-popover z-50 w-[400px] rounded-2xl bg-white shadow-lg",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"mb-5 text-xl font-medium text-gray-900",children:p}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("p",{className:"mb-2 text-base text-gray-500",children:"Type the mail of the person you want to invite:"}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx("label",{className:"mb-2 block font-medium text-gray-700",children:"Email"}),e.jsx("input",{type:"text",value:i,onChange:h=>y(h.target.value),className:"h-full w-full rounded-xl border  border-gray-300 bg-gray-50 bg-transparent px-2 py-3 outline-none"})]})]}),e.jsx("p",{children:s})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 border-t border-gray-200 p-5",children:[e.jsx("button",{className:"rounded-xl border border-gray-300 px-5 py-2 text-gray-500 ",onClick:c,children:"Cancel"}),e.jsx(L,{className:"rounded-xl bg-[#176448] px-5 py-2 text-white hover:bg-[#176448]",loading:v,onClick:j,children:"Send Invitation"})]})]})})}let le=new O;function Je({onClose:p,group:c,fetchData:s,users:g,user:u,sentInvites:i=[]}){const{dispatch:y}=I.useContext(R),[m,v]=d.useState("");console.log("group",c);const[r,j]=d.useState({selectedUsers:[],selectedUsersIds:[]}),[h,w]=d.useState({}),[S,b]=d.useState(!1),M=l=>{j(a=>({...a,selectedUsers:a.selectedUsers.includes(l)?a.selectedUsers.filter(x=>x!==l):[...a.selectedUsers,l],selectedUsersIds:a.selectedUsersIds.includes(l.id)?a.selectedUsersIds.filter(x=>x!==l.id):[...a.selectedUsersIds,l.id]})),h.selectedUsers&&w(a=>({...a,selectedUsers:""}))},n=l=>{j(a=>({...a,selectedUsers:a.selectedUsers.filter(x=>x!==l)}))},N=l=>`${l.first_name} ${l.last_name}`,_=g.filter(l=>{const a=c.members.some(t=>t.id===l.id),x=i.some(t=>t.invitee_email===l.email&&t.group_id===c.group_id);return!a&&!x}).filter(l=>N(l).toLowerCase().includes(m.toLowerCase())),B=i.filter(l=>l.status==="pending"&&l.group_id===c.group_id),o=g.filter(l=>r.selectedUsers.includes(l.id));console.log("formData",r);const P=async()=>{if(c&&c.group_owner_id&&parseInt(c.group_owner_id)!==parseInt(u==null?void 0:u.id)){U(y,"Only the group owner can add members",3e3,"error");return}if(r.selectedUsers.length===0){U(y,"Please select at least one user",5e3,"warning");return}b(!0);try{const l=r.selectedUsers.map(async x=>{const t=g.find(f=>f.id===x.id);if(t){const f=`${window.location.origin}/user/group/accept-invite/${c.group_id}`;await le.callRawAPI(`/v3/api/custom/courtmatchup/user/groups/invite/${c.group_id}`,{email:t.email,link:f},"POST")}});await Promise.all(l);const a={id:c.group_id,members:r.selectedUsers};console.log("payload",a),await Ce(le,{user_id:u==null?void 0:u.id,activity_type:J.group,action_type:W.CREATE,data:a,club_id:u==null?void 0:u.club_id,description:`Added ${r.selectedUsers.length} members to group`}),U(y,"Members invited successfully",5e3,"success"),await s(),p()}catch(l){console.error("Error adding users:",l),U(y,l.message,5e3,"error")}finally{b(!1)}};return e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:e.jsxs("div",{className:"space-y-4",children:[B.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium text-gray-700",children:"Pending Invites"}),e.jsx("div",{className:"flex flex-wrap gap-2 rounded-lg bg-gray-50 p-2",children:B.map(l=>{var a,x;return e.jsxs("div",{className:"flex items-center space-x-1 rounded-full bg-white px-2 py-1 shadow-sm",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center overflow-hidden rounded-full bg-gray-200",children:e.jsx("span",{className:"text-xs text-gray-500",children:((x=(a=l.invitee_email)==null?void 0:a.charAt(0))==null?void 0:x.toUpperCase())||"?"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:l.invitee_email}),e.jsx("span",{className:"ml-1 rounded-full bg-yellow-100 px-2 py-0.5 text-xs text-yellow-800",children:"Pending"})]},l.invite_id)})})]}),o.length>0&&e.jsx("div",{className:"flex flex-wrap gap-2 rounded-lg bg-gray-50 p-2",children:o.map(l=>e.jsxs("div",{className:"flex items-center space-x-1 rounded-full bg-white px-2 py-1 shadow-sm",children:[e.jsx("div",{className:"h-6 w-6 overflow-hidden rounded-full",children:e.jsx("img",{src:(l==null?void 0:l.photo)||"/default-avatar.png",alt:N(l),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:N(l)}),e.jsx("button",{type:"button",onClick:()=>n(l.id),className:"ml-1 rounded-full p-0.5 hover:bg-gray-100",children:e.jsx(H,{className:"h-4 w-4 text-gray-500"})})]},l.id))}),e.jsxs("div",{className:"relative",children:[e.jsx(V,{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"search by name",value:m,onChange:l=>v(l.target.value),className:"block w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-primaryBlue"})]}),e.jsx("div",{className:`max-h-96 overflow-y-auto rounded-xl bg-gray-50 p-2 ${h.selectedUsers?"border-2 border-red-500":""}`,children:_.map(l=>e.jsxs("div",{className:"flex items-center space-x-3 py-2",children:[e.jsx("input",{type:"checkbox",checked:r.selectedUsersIds.includes(l.id),onChange:()=>M(l),className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsx("div",{className:"h-8 w-8 overflow-hidden rounded-full",children:e.jsx("img",{src:(l==null?void 0:l.photo)||"/default-avatar.png",alt:N(l),className:"h-full w-full object-cover"})}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:N(l)})]},l.id))}),h.selectedUsers&&e.jsx("p",{className:"text-sm text-red-500",children:h.selectedUsers})]})}),e.jsxs("div",{className:"sticky bottom-0 mt-auto flex justify-end gap-4 border-t border-gray-200 bg-white px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:p,children:"Cancel"}),e.jsx(L,{type:"button",className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:P,loading:S,children:S?"Saving...":"Add members"})]})]})}let T=new O;function We({title:p,onClose:c,group:s,user:g}){const[u,i]=d.useState((s==null?void 0:s.name)||""),{dispatch:y}=d.useContext(R),[m,v]=d.useState(!1);console.log(s);const r=(s==null?void 0:s.group_owner_id)&&parseInt(s.group_owner_id)===parseInt(g==null?void 0:g.id),j=async()=>{if(!r){U(y,"Only the group owner can edit the group name",3e3,"error");return}try{v(!0),T.setTable("user_groups");const h={id:s.group_id,name:u},w=await T.callRestAPI(h,"PUT");T.setTable("activity_logs"),await T.callRestAPI({user_id:g==null?void 0:g.id,action:"Updated group name",activity_type:J.group,action_type:W.UPDATE,data:JSON.stringify({group:s}),club_id:g==null?void 0:g.club_id,description:"Updated group name"},"POST"),w.message&&(U(y,"Group name updated successfully"),c(!0))}catch(h){U(y,h.message,3e3,"error")}finally{v(!1)}};return e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"contact-info-popover z-50 w-[400px] rounded-2xl bg-white shadow-lg",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"mb-5 text-xl font-medium text-gray-900",children:p}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("p",{className:"mb-2 text-base text-gray-500",children:"Enter new name for the group:"}),e.jsxs("div",{className:"relative flex-1",children:[e.jsx("label",{className:"mb-2 block font-medium text-gray-700",children:"Group Name"}),e.jsx("input",{type:"text",value:u,onChange:h=>i(h.target.value),disabled:!r,className:`h-full w-full rounded-xl border border-gray-300 bg-gray-50 bg-transparent px-2 py-3 outline-none ${r?"":"cursor-not-allowed opacity-50"}`}),!r&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Only the group owner can edit the group name."})]})]})})]}),e.jsxs("div",{className:"flex justify-end gap-2 border-t border-gray-200 p-5",children:[e.jsx("button",{className:"rounded-xl border border-gray-300 px-5 py-2 text-gray-500",onClick:()=>c(!1),children:"Cancel"}),e.jsx(L,{className:`rounded-xl px-5 py-2 text-white ${r?"bg-[#176448] hover:bg-[#176448]":"cursor-not-allowed bg-gray-400"}`,loading:m,onClick:j,disabled:!r,children:"Update Name"})]})]})})}export{Ze as A,Qe as C,We as E,De as G,He as I,Ve as U,Je as a};
