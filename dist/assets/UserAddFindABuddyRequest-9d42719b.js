import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,f as We,b as Ve}from"./vendor-851db8c1.js";import"./BottomDrawer-f0d615b3.js";import{M as ze,T as Xe,G as Ze,u as et,m as tt,aB as le,b as O,am as at,e as st,a5 as de,d as nt,a8 as ot,aF as ce,E as rt,J as it}from"./index-3b44b02c.js";import{B as lt}from"./BackButton-11ba52b2.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{T as dt}from"./TimeSlots-190fee7e.js";import{A as ct}from"./AddPlayers-6be570d7.js";import{S as ut}from"./SportTypeSelection-ee0cc3da.js";import{C as mt}from"./Calendar-9031b5fe.js";import{B as pt}from"./ReservationSummary-260408bc.js";import{S as gt}from"./react-select-c8303602.js";import{g as yt}from"./customThresholdUtils-f40b07d5.js";import{f as ft}from"./date-fns-07266b7d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./react-tooltip-7a26650a.js";import"./index.esm-09a3a6b8.js";import"./SelectionOptionsCard-0d5c6ddd.js";import"./SelectionOption-01b973e9.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";let k=new ze,P=new Xe;const va=({})=>{var ee,te,ae,se,ne,oe,re,ie;const[C,ue]=o.useState(null);o.useState(null),o.useState(null);const[f,me]=o.useState(null),[w,G]=o.useState(new Date),[ht,pe]=o.useState(null),[g,ge]=o.useState([]),[ye,fe]=o.useState([]),[s,he]=o.useState(null),[D,xe]=o.useState(null),[R,ve]=o.useState(null),[N,xt]=o.useState(0),[vt,U]=o.useState(0),[J,St]=o.useState([]),[A,Se]=o.useState([]),{state:bt,dispatch:B}=o.useContext(Ze),[be,Q]=o.useState(!1),[wt,we]=o.useState({from:null,until:null}),[F,Ne]=o.useState([{from:null,until:null}]),[_e,je]=o.useState(""),[u,q]=o.useState([]),[Te,ke]=o.useState(!1),[_,j]=o.useState(1),[Ce,H]=o.useState(!1),[K,De]=o.useState(""),[I,L]=o.useState(1),[W,Re]=o.useState(3.5),[V,Be]=o.useState(3.5),[Fe,Nt]=o.useState(null),[qe,_t]=o.useState(!1),[h,Y]=o.useState(1),[M,Ie]=o.useState([]),[r,z]=o.useState(null),{user_subscription:y,user_permissions:x,club_membership:T}=et(),a=o.useMemo(()=>!(y!=null&&y.planId)||!(T!=null&&T.length)?null:T.find(e=>e.plan_id===y.planId),[y,T]),E=o.useMemo(()=>{var l,p;if(((l=a==null?void 0:a.advance_booking_enabled)==null?void 0:l.buddy)===!1){const c=new Date;return c.setFullYear(c.getFullYear()+10),c}const e=((p=a==null?void 0:a.advance_booking_days)==null?void 0:p.buddy)||10,n=new Date,i=new Date;return i.setDate(n.getDate()+e),i},[a]),[v,S]=o.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),X=We(),m=localStorage.getItem("user"),Ee=async()=>{try{const e=await P.getOne("user",m,{}),n=await k.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${e.model.club_id}`,{},"GET");Se(n.sports),he(n.model)}catch(e){console.error(e)}},$e=async()=>{try{const e=await P.getList("user",{filter:["role,cs,user"]});ge(e.list)}catch(e){console.error(e)}},Oe=async()=>{try{const e=await k.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");fe(e.groups)}catch(e){console.error(e)}},Ae=async()=>{try{const e=await P.getList("user",{filter:[`guardian,eq,${m}`,"role,cs,user"]});Ie(e.list)}catch(e){console.error("Error fetching family members:",e)}};o.useEffect(()=>{(async()=>(H(!0),await Ee(),await $e(),await Oe(),await Ae(),H(!1)))()},[]),Ve.useEffect(()=>{tt({path:"/user/create-request",clubName:s==null?void 0:s.name,favicon:s==null?void 0:s.club_logo,description:"Create Request"})},[s==null?void 0:s.club_logo]);const Le=()=>{G(new Date(w.setMonth(w.getMonth()-1)))},Ye=()=>{G(new Date(w.setMonth(w.getMonth()+1)))},Me=async()=>{var i,l;if(!(y!=null&&y.planId)){S({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to use the Find a Buddy feature",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(x!=null&&x.allowBuddy)){S({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${x==null?void 0:x.planName}) does not include the Find a Buddy feature. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(f>E&&((i=a==null?void 0:a.advance_booking_enabled)==null?void 0:i.buddy)!==!1){const p=((l=a==null?void 0:a.advance_booking_days)==null?void 0:l.buddy)||10;S({isOpen:!0,title:"Date Selection Error",message:`Your membership plan only allows creating buddy requests ${p} days in advance. Please select a valid date.`,type:"warning"});return}if(!C||!f||!F.length||!D||!R){S({isOpen:!0,title:"Incomplete Details",message:"Please fill in all required fields",type:"warning"});return}const{start_time:e,end_time:n}=ot(F);Q(!0);try{const p=new Date(f),c=F.filter(b=>b.from&&b.until).map(b=>({start_time:ce(b.from),end_time:ce(b.until)}));if(c.length===0){S({isOpen:!0,title:"Time Slots Required",message:"Please select at least one valid time slot",type:"warning"});return}const $={sport_id:C,slots:c,ntrp:W,max_ntrp:V,num_players:I,num_needed:_,type:D,sub_type:R,need_coach:1,notes:K,date:ft(p,"yyyy-MM-dd"),start_time:e,end_time:n,player_ids:u.map(b=>b.id),primary_player_id:(r==null?void 0:r.id)||parseInt(m)},Ke=await k.callRawAPI("/v3/api/custom/courtmatchup/user/buddy/create-request",$,"POST");k.setTable("activity_logs");const jt=await k.callRestAPI({activity_type:rt.find_a_buddy,user_id:m,club_id:s==null?void 0:s.id,action_type:it.CREATE,data:JSON.stringify($),description:"Created a find a buddy request"},"POST");Ke.error||(O(B,"Request created successfully",3e3,"success"),X("/user/find-a-buddy"))}catch(p){console.error(p),S({isOpen:!0,title:"Request Error",message:p.message||"Error creating buddy request",type:"error"})}finally{Q(!1)}},Pe=e=>{we(e),pe(e.from)},Ge=e=>{Ne(e)};o.useEffect(()=>{if(N&&(u!=null&&u.length)){const e=N*(u==null?void 0:u.length),n=le(s==null?void 0:s.fee_settings,e),i=(s==null?void 0:s.club_fee)||0;U(e+n+i)}else{const e=le(s==null?void 0:s.fee_settings,N),n=(s==null?void 0:s.club_fee)||0;U(N+e+n)}},[N,u,s==null?void 0:s.fee_settings,s==null?void 0:s.club_fee]);const Ue=()=>{j(e=>Math.min(e+1,d))},Je=()=>{j(e=>Math.max(e-1,0))},Qe=()=>{Y(2)},He=e=>{q(n=>n.some(l=>l.id===e.id)?n.filter(l=>l.id!==e.id):[...n,e])},Z=e=>{const n=e.value||e;(n==null?void 0:n.id)!==(r==null?void 0:r.id)&&(z(n),q(i=>{const l=i.filter(c=>c.id!==(r==null?void 0:r.id));if(l.some(c=>c.id===n.id)){const c=l.filter($=>$.id!==n.id);return[n,...c]}else return[n,...l]}))},d=yt(s==null?void 0:s.custom_request_threshold,C,D,R,4,A);return o.useEffect(()=>{if(g.length>0&&!r){const e=g.find(n=>n.id===parseInt(m));e&&z(e)}},[g,r,m]),o.useEffect(()=>{u.length>d&&(console.log(`Clearing selected players: current ${u.length} exceeds new threshold ${d}`),q([]),j(1),L(1),O(B,`Player selection cleared. New maximum is ${d} players. Please select players again.`,4e3,"warning"))},[d]),o.useEffect(()=>{_>d&&j(Math.min(_,d)),I>d&&L(Math.min(I,d))},[d]),t.jsxs("div",{className:"",children:[t.jsx(at,{isOpen:v.isOpen,onClose:()=>S({...v,isOpen:!1}),title:v.title,message:v.message,actionButtonText:v.actionButtonText,actionButtonLink:v.actionButtonLink,type:v.type}),Ce&&t.jsx(st,{}),t.jsxs("div",{className:"flex items-center justify-center bg-white p-4",children:[h===1&&t.jsx("div",{className:" ",children:"Step 1 • Select date and time"}),h===2&&t.jsx("div",{className:" ",children:"Step 2 • Reserving details"}),h===3&&t.jsx("div",{className:" ",children:"Step 3 • Payment"})]}),t.jsxs("div",{className:"p-4",children:[t.jsx(lt,{onBack:()=>{h===1?X(-1):Y(h===2?1:2)}}),h===1&&t.jsx("div",{children:t.jsx("div",{className:" p-4",children:t.jsx("div",{className:"space-y-6",children:t.jsx("div",{className:"mx-auto max-w-7xl p-4",children:t.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[t.jsx(ut,{sports:A,userPermissions:x,onSelectionChange:({sport:e,type:n,subType:i})=>{ue(e),xe(n),ve(i)}}),t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[((ee=a==null?void 0:a.advance_booking_enabled)==null?void 0:ee.buddy)===!1?t.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can create a find-a-buddy request for any future date."}):((te=a==null?void 0:a.advance_booking_days)==null?void 0:te.buddy)!==void 0&&t.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can create a find-a-buddy request up to"," ",(ae=a==null?void 0:a.advance_booking_days)==null?void 0:ae.buddy," ",((se=a==null?void 0:a.advance_booking_days)==null?void 0:se.buddy)===1?"day":"days"," ","in advance."]}),t.jsx(mt,{currentMonth:w,selectedDate:f,onDateSelect:e=>{var n,i;if(e>E&&((n=a==null?void 0:a.advance_booking_enabled)==null?void 0:n.buddy)!==!1){const l=((i=a==null?void 0:a.advance_booking_days)==null?void 0:i.buddy)||10;O(B,`Your membership plan only allows booking ${l} days in advance`,3e3,"warning");return}me(e)},onPreviousMonth:Le,onNextMonth:Ye,daysOff:s!=null&&s.days_off?JSON.parse(s.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:E,disabledDateMessage:((ne=a==null?void 0:a.advance_booking_enabled)==null?void 0:ne.buddy)===!1?"You can book for any future date":`Your membership plan only allows booking ${((oe=a==null?void 0:a.advance_booking_days)==null?void 0:oe.buddy)||10} days in advance`})]}),t.jsx(dt,{isLoading:qe,selectedDate:f,timeRange:J,onTimeClick:Pe,onTimeSlotsChange:Ge,onNext:()=>{var e,n;if(f>E&&((e=a==null?void 0:a.advance_booking_enabled)==null?void 0:e.buddy)!==!1){const i=((n=a==null?void 0:a.advance_booking_days)==null?void 0:n.buddy)||10;O(B,`Your membership plan only allows booking ${i} days in advance`,3e3,"warning");return}Qe()},nextButtonText:"Next: Players",startHour:0,clubTimes:s!=null&&s.times?JSON.parse(s.times):[],endHour:24,interval:30,isTimeSlotAvailable:()=>!0,multipleSlots:!0})]})})})})}),h===2&&t.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[t.jsx(pt,{selectedSport:C,sports:A,selectedType:D,selectedSubType:R,selectedDate:f,selectedTimes:J,timeSlots:F}),t.jsxs("div",{className:"space-y-4",children:[M.length>0&&t.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[t.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-900",children:"Request for"}),t.jsx(gt,{className:"w-full text-sm",options:[{value:g.find(e=>e.id===parseInt(m)),label:`${(re=g.find(e=>e.id===parseInt(m)))==null?void 0:re.first_name} ${(ie=g.find(e=>e.id===parseInt(m)))==null?void 0:ie.last_name} (You)`},...M.map(e=>({value:e,label:`${e.first_name} ${e.last_name} (${e.family_role||"Family Member"})`}))],onChange:Z,value:r?{value:r,label:r.id===parseInt(m)?`${r.first_name} ${r.last_name} (You)`:`${r.first_name} ${r.last_name} (${r.family_role||"Family Member"})`}:null,placeholder:"Select who this request is for",isSearchable:!1}),t.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Choose who this buddy request is for"})]}),t.jsx(ct,{searchQuery:_e,setSearchQuery:je,selectedPlayers:u,setSelectedPlayers:q,players:g,groups:ye,selectedGroup:Fe,isFindBuddyEnabled:Te,setIsFindBuddyEnabled:ke,playersNeeded:_,handleIncrement:Ue,handleDecrement:Je,onPlayerToggle:He,showAddReservationToFindBuddy:!1,showPlayersNeeded:!1,maximumPlayers:d,familyMembers:M,currentUser:r,onCurrentUserChange:Z,userProfile:g.find(e=>e.id===parseInt(m))})]}),t.jsxs("div",{className:"h-fit rounded-xl bg-white shadow-5",children:[t.jsx("div",{className:"rounded-xl bg-gray-50 p-4 text-center",children:t.jsx("h2",{className:"text-base font-medium",children:"Other details"})}),t.jsx("div",{className:"p-4",children:t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"mb-2 text-base font-medium",children:"My group NTRP score"}),t.jsxs("div",{className:"flex gap-4",children:[t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"relative overflow-hidden rounded-xl border border-gray-200 bg-white",children:[t.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",children:"Min"}),t.jsx("select",{value:W,onChange:e=>Re(e.target.value),className:"w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0",children:de.map(e=>t.jsx("option",{value:e,children:e.toFixed(1)},e))})]})}),t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"relative overflow-hidden rounded-xl border border-gray-200 bg-white",children:[t.jsx("div",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-gray-400",children:"Max"}),t.jsx("select",{value:V,onChange:e=>Be(e.target.value),className:"w-full appearance-none rounded-lg border-0 py-3 pl-12 pr-8 text-right focus:ring-0",children:de.map(e=>t.jsx("option",{value:e,children:e.toFixed(1)},e))})]})})]})]}),t.jsxs("div",{className:"mb-2 flex items-center justify-between gap-2",children:[t.jsx("h3",{className:" text-base font-medium",children:"Players playing"}),t.jsx("div",{className:"",children:t.jsx("select",{value:I,onChange:e=>L(e.target.value),className:"w-fit appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8",children:[...Array(d)].map((e,n)=>t.jsx("option",{value:n+1,children:n+1},n))})})]}),t.jsxs("div",{className:"mb-2 flex items-center justify-between gap-2",children:[t.jsx("h3",{className:" text-base font-medium",children:"Players needed"}),t.jsx("div",{className:"relative",children:t.jsx("select",{value:_,onChange:e=>j(e.target.value),className:"w-full appearance-none rounded-xl border border-gray-200 bg-white py-2 pl-3 pr-8",children:[...Array(d)].map((e,n)=>t.jsx("option",{value:n+1,children:n+1},n))})})]}),t.jsxs("div",{children:[t.jsxs("h3",{className:"mb-2 text-base font-medium",children:["Short bio"," ",t.jsx("span",{className:"text-gray-400",children:"(Optional)"})]}),t.jsx("textarea",{className:"w-full rounded-xl border border-gray-200 p-3 focus:border-gray-200 focus:ring-0",rows:"3",value:K,onChange:e=>De(e.target.value)})]}),t.jsx(nt,{loading:be,onClick:Me,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:"Submit request"})]})})]})]})]})]})};export{va as default};
