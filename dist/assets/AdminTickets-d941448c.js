import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as t,r as F,f as pe}from"./vendor-851db8c1.js";import{M as H,G as V,aw as O,d as P,b as D,a4 as ge,T as fe,A as ye,c as je,X as ve,R as be,e as Ne,t as E}from"./index-3b44b02c.js";import"./index-be4468eb.js";import"./yup-54691517.js";import"./AddButton.module-98aac587.js";import{P as Se}from"./index-eb1bc208.js";import"./lodash-91d5d207.js";import ke from"./Skeleton-1e8bf077.js";import"./numeral-ea653b2a.js";import{F as we,f as Ce}from"./FormattedPhoneNumber-40dd7178.js";import{f as G}from"./date-fns-07266b7d.js";const z=new H;function _e({data:a,onResolveClick:f,onEscalate:v}){const{dispatch:p}=t.useContext(V),[n,m]=F.useState(null),x=o=>G(new Date(o),"MMM dd, yyyy • hh:mm a"),l=localStorage.getItem("role"),y=o=>{navigator.clipboard.writeText(o),D(p,"Copied to clipboard",3e3,"success")},w=async()=>{z.setTable("user");const o=await z.callRestAPI({id:a.resolved_by},"GET");m(o.model)};return F.useEffect(()=>{a.resolved_by&&w()},[a.resolved_by]),e.jsxs("div",{className:"relative flex flex-col rounded-lg bg-white",children:[e.jsxs("div",{className:"flex-1 space-y-6 ",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"Created on"}),e.jsx("p",{className:"mt-1 font-medium text-gray-900",children:x(a.create_at)})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"Created by"}),e.jsx("p",{className:"mt-1 font-medium text-gray-900",children:a.user.first_name?`${a.user.first_name} ${a.user.last_name}`:"--"})]}),e.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"EMAIL"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("p",{className:"font-medium text-gray-900 underline",children:a.user.email}),e.jsx("button",{onClick:()=>y(a.user.email),className:"text-gray-500 hover:text-gray-700",children:e.jsx(O,{size:20})})]})]}),e.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"PHONE"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2",children:[e.jsx("p",{className:"font-medium text-gray-900 underline",children:e.jsx(we,{phoneNumber:a.user.phone,format:"US"})}),e.jsx("button",{onClick:()=>{const o=a.user.phone?Ce(a.user.phone,"US"):"************";y(o)},className:"text-gray-500 hover:text-gray-700",children:e.jsx(O,{size:20})})]})]}),a.resolved===1?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border-t border-gray-200"}),e.jsxs("div",{className:"rounded-xl bg-gray-100 p-3",children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"Resolved on"}),e.jsx("p",{className:"mt-1 font-medium text-gray-900",children:x(a.resolved_date)})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"Resolved by"}),e.jsx("p",{className:"mt-1 text-gray-900",children:n?`${n.first_name||"--"} ${n.last_name||"--"} (${n.email||"--"})`:"N/A"})]})]}):null,e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"Request"}),e.jsx("p",{className:"mt-1 text-gray-900",children:a.request})]}),a.note&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-sm text-gray-600",children:"Internal note"}),e.jsx("p",{className:"mt-1 text-gray-900",children:a.note})]})]}),e.jsx("div",{className:"fixed  bottom-0 left-0 w-full border-t p-4",children:a.resolved===0?e.jsx(e.Fragment,{children:a.escalated&&l!=="admin"?e.jsx(e.Fragment,{children:e.jsx("div",{className:"text-center text-gray-600",children:"This ticket has been escalated and will be handled by Court Matchup support team."})}):e.jsxs("div",{className:"flex gap-2",children:[e.jsx(P,{onClick:f,className:"w-full rounded-lg bg-primaryBlue py-3 text-white transition-colors",children:"Mark as resolved"}),l!=="admin"&&e.jsx(P,{onClick:v,className:"w-full rounded-lg border border-gray-300 bg-white py-3 transition-colors",children:"Escalate"})]})}):e.jsxs("div",{className:"text-center text-gray-600",children:["Resolved on ",x(a.resolved_date)]})})]})}function Te({isOpen:a,onClose:f,onResolve:v,resolveLoading:p}){const[n,m]=t.useState(""),x=async()=>{await v(n),m("")};return a?e.jsx("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"mx-4 w-full max-w-md rounded-2xl bg-white",children:e.jsxs("div",{className:"space-y-4 py-5",children:[e.jsx("h2",{className:"px-6 text-xl font-semibold",children:"Mark as resolved"}),e.jsxs("div",{className:"space-y-2 px-6 ",children:[e.jsxs("label",{className:"block",children:[e.jsx("span",{className:"text-gray-700",children:"Internal note"}),e.jsx("span",{className:"ml-1 text-gray-500",children:"(Optional)"})]}),e.jsx("textarea",{className:"min-h-[120px] w-full rounded-lg border border-gray-200 p-3 focus:outline-none focus:ring-2 focus:ring-[#176448]",value:n,onChange:l=>m(l.target.value),placeholder:"Add your internal note here..."}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(ge,{size:16}),e.jsx("span",{children:"This note will not be shared with the user."})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t border-gray-200 px-4 pt-4",children:[e.jsx("button",{onClick:f,className:"rounded-lg border px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx(P,{loading:p,onClick:x,className:"rounded-lg bg-[#176448] px-4 py-2 text-white hover:bg-[#13513d]",children:"Mark as resolved"})]})]})})}):null}let Re=new H,M=new fe;const q=[{header:"Created on",accessor:"create_at"},{header:"Created by",accessor:"user"},{header:"Message",accessor:"request"},{header:"Club",accessor:"club"},{header:"Esclated",accessor:"escalated"},{header:"",accessor:"actions"}],Ke=({user:a,status:f,club:v})=>{const{dispatch:p}=t.useContext(V),{dispatch:n}=t.useContext(ye),[m,x]=t.useState([]),[l,y]=t.useState(10),[w,o]=t.useState(0),[Ee,K]=t.useState(0),[h,Q]=t.useState(1),[U,X]=t.useState(!1),[Y,J]=t.useState(!1),[Me,W]=t.useState(!1),[C,_]=t.useState(!0),[Z,Pe]=t.useState(!1),[ee,De]=t.useState(!1),[se,b]=t.useState(!1),[T,N]=t.useState(null);pe();const L=t.useRef(null),[te,R]=t.useState(!1),[ae,I]=t.useState(!1),[re,$]=t.useState(!1),[le,ne]=t.useState(""),[A,ce]=t.useState(null);function oe(){i(h-1,l)}function ie(){i(h+1,l)}async function i(s,r,c={},d=[]){_(!(ee||Z));try{const u=await M.getPaginate("ticket",{join:["user|user_id","clubs|club_id"],page:s,limit:r,filter:[...d,`resolved,eq,${f}`]});u&&_(!1);const{total:S,limit:k,num_pages:j,page:g,list:he}=u;x(he),y(k),o(j),Q(g),K(S),X(g>1),J(g+1<=j)}catch(u){_(!1),console.log("ERROR",u),E(n,u.message)}}const de=s=>{const r=s.target.value;ne(r),A&&clearTimeout(A);const c=setTimeout(()=>{r.trim()?i(h,l,{},[`${Re._project_id}_user.first_name,cs,${r}`]):i(h,l)},500);ce(c)};t.useEffect(()=>{const r=setTimeout(async()=>{await i(1,l)},700);return()=>{clearTimeout(r)}},[]);const B=s=>{L.current&&!L.current.contains(s.target)&&W(!1)};t.useEffect(()=>(document.addEventListener("mousedown",B),()=>{document.removeEventListener("mousedown",B)}),[]);const ue=localStorage.getItem("user"),me=async s=>{I(!0);try{const c=G(new Date,"yyyy-MM-dd HH:mm:ss");await M.update("ticket",T.id,{resolved:1,resolved_by:ue,resolved_date:c,note:s}),R(!1),b(!1),N(null),i(h,l),D(p,"Ticket resolved successfully",3e3,"success")}catch(r){console.error("Error resolving ticket:",r),E(n,r.message)}finally{I(!1)}},xe=async()=>{$(!0);try{await M.update("ticket",T.id,{escalated:1}),b(!1),N(null),i(h,l),D(p,"Ticket escalated successfully",3e3,"success")}catch(s){console.error("Error resolving ticket:",s),E(n,s.message)}finally{$(!1)}};return e.jsxs("div",{className:"h-screen w-full",children:[e.jsxs("div",{className:"relative flex max-w-[200px] flex-1 items-center",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(je,{className:"h-5 w-5 text-gray-500"})}),e.jsx("input",{type:"text",value:le,onChange:de,className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search user"})]}),C?e.jsx(ke,{}):e.jsxs("div",{className:"",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full table-auto border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsx("tr",{children:q.map((s,r)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:s.header},r))})}),e.jsx("tbody",{className:" ",children:m.map((s,r)=>e.jsx("tr",{className:"hover:bg-gray-40 rounded-xl bg-gray-100 px-4 py-3 text-gray-500",children:q.map((c,d)=>{var u,S,k,j,g;return c.accessor==="actions"?e.jsx("td",{className:"whitespace-nowrap rounded-r-xl px-6 py-4",children:e.jsx("button",{onClick:()=>{b(!0),N(s)},children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})},d):c.accessor==="create_at"?e.jsx("td",{className:"whitespace-nowrap rounded-l-xl px-6 py-4",children:ve(s==null?void 0:s.create_at)},d):c.accessor==="user"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:!((u=s==null?void 0:s.user)!=null&&u.first_name)||!((S=s==null?void 0:s.user)!=null&&S.last_name)?"--":`${(k=s==null?void 0:s.user)==null?void 0:k.first_name} ${(j=s==null?void 0:s.user)==null?void 0:j.last_name}`},d):c.accessor==="club"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((g=s==null?void 0:s.clubs)==null?void 0:g.name)||"--"},d):c.accessor==="escalated"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(s==null?void 0:s.escalated)==1?"Yes":"No"},d):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:s[c.accessor]},d)})},r))})]})}),C&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!C&&m.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(Se,{currentPage:h,pageCount:w,pageSize:l,canPreviousPage:U,canNextPage:Y,updatePageSize:s=>{y(s),i(1,s)},previousPage:oe,nextPage:ie,gotoPage:s=>i(s,l)}),e.jsx(be,{isOpen:se,showFooter:!1,onClose:()=>{b(!1),N(null)},title:"Ticket Details",children:e.jsx(_e,{data:T,onResolveClick:()=>R(!0),onEscalate:xe})}),e.jsx(Te,{isOpen:te,onClose:()=>R(!1),onResolve:me,resolveLoading:ae}),re&&e.jsx(Ne,{})]})};export{Ke as A};
