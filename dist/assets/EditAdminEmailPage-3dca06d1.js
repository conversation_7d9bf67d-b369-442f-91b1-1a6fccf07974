import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as k,f as R,r as o,j as G}from"./vendor-851db8c1.js";import{u as B}from"./react-hook-form-687afde5.js";import{o as D}from"./yup-2824f222.js";import{c as M,a as m}from"./yup-54691517.js";import{w as O,M as U,A as H,G as K,t as S,b as V}from"./index-3b44b02c.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let n=new U;const Y=({activeId:C,setSidebar:c})=>{var b,f,j,y,w,E;const F=M({subject:m().required(),html:m().required(),tag:m().required()}).required(),{dispatch:d}=k.useContext(H),{dispatch:p}=k.useContext(K),T=R(),[A,P]=o.useState(0),[u,$]=o.useState(""),[x,h]=o.useState(!1),{register:r,handleSubmit:q,setError:g,setValue:l,formState:{errors:a}}=B({resolver:D(F)});G(),o.useEffect(function(){p({type:"SETPATH",payload:{path:"email"}}),async function(){try{n.setTable("email");const t=await n.callRestAPI({id:C},"GET");t.error||(l("subject",t.model.subject),l("html",t.model.html),l("tag",t.model.tag),$(t.model.slug),P(t.model.id))}catch(t){console.log("error",t),S(d,t.message)}}()},[]);const I=async t=>{h(!0);try{const s=await n.callRestAPI({id:A,slug:u,subject:t.subject,html:t.html,tag:t.tag},"PUT");if(!s.error)V(p,"Updated"),T("/admin/email");else if(s.validation){const N=Object.keys(s.validation);for(let i=0;i<N.length;i++){const v=N[i];g(v,{type:"manual",message:s.validation[v]})}}}catch(s){console.log("Error",s),g("html",{type:"manual",message:s.message}),S(d,s.message)}h(!1)};return e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Email"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#F4F4F4]",onClick:()=>c(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await q(I)(),c(!1)},disabled:x,children:x?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full p-4 text-left",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",value:u,readOnly:!0,className:"focus:shadow-outline} mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...r("subject"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(b=a.subject)!=null&&b.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(f=a.subject)==null?void 0:f.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...r("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(j=a.tag)!=null&&j.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(y=a.tag)==null?void 0:y.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(w=a.html)!=null&&w.message?"border-red-500":""}`,...r("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(E=a.html)==null?void 0:E.message})]})]})]})},Fe=O(Y,"email","You don't have permission to edit emails");export{Fe as default};
