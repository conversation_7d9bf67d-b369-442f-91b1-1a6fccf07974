import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as V}from"./vendor-851db8c1.js";import{u as G}from"./react-hook-form-687afde5.js";import{o as L}from"./yup-2824f222.js";import{c as R,a as o}from"./yup-54691517.js";import{M as I,A as _,G as $,t as T,b as K}from"./index-3b44b02c.js";import{D as M}from"./index-85ab7a01.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-07266b7d.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let S=new I;const Te=({activeId:m,setSidebar:c})=>{var v,j,w;const P=R({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),d=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:p}=a.useContext(_),{dispatch:u}=a.useContext($),[A,U]=a.useState((v=d[0])==null?void 0:v.key),[x,y]=a.useState(""),[q,D]=a.useState(""),[g,r]=a.useState(!1),F=V(),{register:l,handleSubmit:f,setError:h,setValue:i,formState:{errors:b}}=G({resolver:L(P)}),k=async e=>{r(!0);let N=new I;r(!0);try{N.setTable("cms");const s=await N.cmsEdit(q,e.page,e.key,e.type,x);if(!s.error)F("/admin/cms"),K(u,"Updated");else if(s.validation){const C=Object.keys(s.validation);for(let n=0;n<C.length;n++){const E=C[n];h(E,{type:"manual",message:s.validation[E]})}}}catch(s){console.log("Error",s),h("page",{type:"manual",message:s.message}),T(p,s.message)}r(!1)};return a.useEffect(()=>{u({type:"SETPATH",payload:{path:"cms"}}),async function(){try{S.setTable("cms");const e=await S.callRestAPI({id:m},"GET");console.log("result: ",e),e.error||(D(e.model.id),i("page",e.model.page),i("type",e.model.content_type),i("key",e.model.content_key),y(e.model.content_value))}catch(e){console.log("Error",e),T(p,e.message)}}()},[m]),t.jsxs("div",{className:"mx-auto rounded",children:[t.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>c(!1),children:"Cancel"}),t.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await f(k)(),c(!1)},disabled:g,children:g?"Saving...":"Save"})]})]}),t.jsxs("form",{className:"w-full p-4 text-left",onSubmit:f(k),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),t.jsx("input",{type:"text",placeholder:"Page",...l("page"),className:"shadow appearance-none border rounded w-full mb-3 py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline}"})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),t.jsx("input",{type:"text",placeholder:"Content Identifier",...l("key"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(j=b.key)!=null&&j.message?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:(w=b.key)==null?void 0:w.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Content Type"}),t.jsx("select",{name:"type",id:"type",className:"shadow border  rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline",...l("type",{onChange:e=>U(e.target.value)}),children:d.map(e=>t.jsx("option",{name:e.name,value:e.key,children:e.value},e.key))})]}),t.jsx(M,{contentValue:x,contentType:A,setContentValue:y})]})]})};export{Te as default};
